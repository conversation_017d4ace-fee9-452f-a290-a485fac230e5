/**
 * 品牌模型
 * 定义品牌数据结构和业务逻辑
 */

const mongoose = require('mongoose');
const { BRAND_STATUS, VALIDATION_RULES } = require('../constants');

/**
 * 品牌 Schema 定义
 */
const brandSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, '品牌名称是必需的'],
    trim: true,
    minlength: [VALIDATION_RULES.BRAND.NAME.MIN_LENGTH, `品牌名称至少需要 ${VALIDATION_RULES.BRAND.NAME.MIN_LENGTH} 个字符`],
    maxlength: [VALIDATION_RULES.BRAND.NAME.MAX_LENGTH, `品牌名称不能超过 ${VALIDATION_RULES.BRAND.NAME.MAX_LENGTH} 个字符`],
    validate: {
      validator: function(v) {
        return VALIDATION_RULES.BRAND.NAME.PATTERN.test(v);
      },
      message: '品牌名称只能包含中文、英文、数字、空格、连字符和下划线'
    }
  },
  
  icon: {
    type: String,
    required: [true, '品牌图标链接是必需的'],
    trim: true,
    validate: {
      validator: function(v) {
        return /^https?:\/\/.+\.(jpg|jpeg|png|gif|svg|webp)$/i.test(v) || /^\/uploads\/.+\.(jpg|jpeg|png|gif|svg|webp)$/i.test(v);
      },
      message: '请提供有效的图片URL或上传路径'
    }
  },

  // 关联的图标文件ID（如果是上传的文件）
  iconFileId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'File',
    default: null
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: [VALIDATION_RULES.BRAND.DESCRIPTION.MAX_LENGTH, `描述不能超过 ${VALIDATION_RULES.BRAND.DESCRIPTION.MAX_LENGTH} 个字符`]
  },
  
  website: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        return !v || VALIDATION_RULES.BRAND.URL.PATTERN.test(v);
      },
      message: 'URL 格式不正确，必须以 http:// 或 https:// 开头'
    }
  },
  
  status: {
    type: String,
    enum: Object.values(BRAND_STATUS),
    default: BRAND_STATUS.ACTIVE,
    index: true
  },
  
  sortOrder: {
    type: Number,
    default: 0,
    index: true
  },
  
  tags: [{
    type: String,
    trim: true
  }],
  
  metadata: {
    views: {
      type: Number,
      default: 0
    },
    clicks: {
      type: Number,
      default: 0
    },
    lastAccessed: {
      type: Date
    }
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

/**
 * 索引定义
 */
brandSchema.index({ name: 1 }, { unique: true });
brandSchema.index({ status: 1, sortOrder: 1 });
brandSchema.index({ createdAt: -1 });
brandSchema.index({ 'metadata.views': -1 });
brandSchema.index({ tags: 1 });

/**
 * 虚拟字段
 */

// 格式化创建时间
brandSchema.virtual('formattedCreatedAt').get(function() {
  return this.createdAt ? this.createdAt.toLocaleDateString('zh-CN') : null;
});

// 格式化更新时间
brandSchema.virtual('formattedUpdatedAt').get(function() {
  return this.updatedAt ? this.updatedAt.toLocaleDateString('zh-CN') : null;
});

// 是否活跃
brandSchema.virtual('isActive').get(function() {
  return this.status === BRAND_STATUS.ACTIVE;
});

// 是否已删除
brandSchema.virtual('isDeleted').get(function() {
  return this.status === BRAND_STATUS.DELETED;
});

// 是否使用上传的图标文件
brandSchema.virtual('hasUploadedIcon').get(function() {
  return !!this.iconFileId;
});

/**
 * 实例方法
 */

// 激活品牌
brandSchema.methods.activate = function() {
  this.status = BRAND_STATUS.ACTIVE;
  return this.save();
};

// 停用品牌
brandSchema.methods.deactivate = function() {
  this.status = BRAND_STATUS.INACTIVE;
  return this.save();
};

// 软删除品牌
brandSchema.methods.softDelete = function() {
  this.status = BRAND_STATUS.DELETED;
  return this.save();
};

// 增加浏览次数
brandSchema.methods.incrementViews = function() {
  this.metadata.views += 1;
  this.metadata.lastAccessed = new Date();
  return this.save();
};

// 增加点击次数
brandSchema.methods.incrementClicks = function() {
  this.metadata.clicks += 1;
  this.metadata.lastAccessed = new Date();
  return this.save();
};

// 添加标签
brandSchema.methods.addTag = function(tag) {
  if (tag && !this.tags.includes(tag)) {
    this.tags.push(tag);
    return this.save();
  }
  return Promise.resolve(this);
};

// 移除标签
brandSchema.methods.removeTag = function(tag) {
  this.tags = this.tags.filter(t => t !== tag);
  return this.save();
};

/**
 * 静态方法
 */

// 获取活跃品牌
brandSchema.statics.getActiveBrands = function(options = {}) {
  const { limit, skip, sortBy = 'sortOrder', sortOrder = 1 } = options;
  
  let query = this.find({ status: BRAND_STATUS.ACTIVE });
  
  // 排序
  const sort = {};
  sort[sortBy] = sortOrder;
  if (sortBy !== 'createdAt') {
    sort.createdAt = -1; // 次要排序
  }
  query = query.sort(sort);
  
  // 分页
  if (skip) query = query.skip(skip);
  if (limit) query = query.limit(limit);
  
  return query;
};

// 按状态获取品牌
brandSchema.statics.getByStatus = function(status, options = {}) {
  const { limit, skip, sortBy = 'createdAt', sortOrder = -1 } = options;
  
  let query = this.find({ status });
  
  // 排序
  const sort = {};
  sort[sortBy] = sortOrder;
  query = query.sort(sort);
  
  // 分页
  if (skip) query = query.skip(skip);
  if (limit) query = query.limit(limit);
  
  return query;
};

// 搜索品牌
brandSchema.statics.search = function(keyword, options = {}) {
  const { status, limit, skip } = options;
  
  const searchRegex = new RegExp(keyword, 'i');
  const query = {
    $or: [
      { name: searchRegex },
      { description: searchRegex },
      { tags: searchRegex }
    ]
  };
  
  if (status) {
    query.status = status;
  }
  
  let mongoQuery = this.find(query);
  
  // 分页
  if (skip) mongoQuery = mongoQuery.skip(skip);
  if (limit) mongoQuery = mongoQuery.limit(limit);
  
  return mongoQuery.sort({ 'metadata.views': -1, createdAt: -1 });
};

// 获取热门品牌
brandSchema.statics.getPopular = function(limit = 10) {
  return this.find({ status: BRAND_STATUS.ACTIVE })
    .sort({ 'metadata.views': -1, 'metadata.clicks': -1 })
    .limit(limit);
};

// 获取最新品牌
brandSchema.statics.getLatest = function(limit = 10) {
  return this.find({ status: BRAND_STATUS.ACTIVE })
    .sort({ createdAt: -1 })
    .limit(limit);
};

// 按标签获取品牌
brandSchema.statics.getByTag = function(tag, options = {}) {
  const { limit, skip } = options;
  
  let query = this.find({ 
    tags: tag,
    status: BRAND_STATUS.ACTIVE 
  });
  
  if (skip) query = query.skip(skip);
  if (limit) query = query.limit(limit);
  
  return query.sort({ sortOrder: 1, createdAt: -1 });
};

// 获取统计信息
brandSchema.statics.getStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalViews: { $sum: '$metadata.views' },
        totalClicks: { $sum: '$metadata.clicks' }
      }
    }
  ]);
};

/**
 * 中间件
 */

// 保存前处理
brandSchema.pre('save', function(next) {
  // 确保名称唯一性（忽略大小写）
  if (this.isModified('name')) {
    this.name = this.name.trim();
  }
  
  // 处理网站 URL
  if (this.isModified('website') && this.website) {
    this.website = this.website.trim();
    if (this.website && !this.website.startsWith('http')) {
      this.website = 'https://' + this.website;
    }
  }
  
  next();
});

// 删除前处理
brandSchema.pre('remove', function(next) {
  // 这里可以添加删除前的清理逻辑
  // 比如删除相关的文件、缓存等
  next();
});

/**
 * 创建模型
 */
const Brand = mongoose.model('Brand', brandSchema);

module.exports = Brand;
