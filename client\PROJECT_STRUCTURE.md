# 前端项目结构重构报告

## 🎯 重构目标

1. **统一 API 基础域名配置**：实现前后端分离，支持灵活的 API 域名配置
2. **现代化项目结构**：参考流行的前端项目组织方案，提升代码可维护性
3. **模块化架构**：将功能按职责分离，提高代码复用性和可测试性

## 🏗️ 新的项目结构

```
client/
├── src/
│   ├── main/                          # 主进程相关文件
│   │   └── main.js                    # Electron 主进程入口
│   │
│   ├── renderer/                      # 渲染进程相关文件
│   │   ├── pages/                     # 页面文件
│   │   │   ├── index.html             # 主页面
│   │   │   ├── login.html             # 登录页面
│   │   │   ├── settings.html          # 设置页面
│   │   │   ├── admin.html             # 管理页面
│   │   │   ├── renderer.js            # 主页面逻辑
│   │   │   ├── login.js               # 登录页面逻辑
│   │   │   ├── settings.js            # 设置页面逻辑
│   │   │   └── admin.js               # 管理页面逻辑
│   │   │
│   │   ├── components/                # 可复用组件
│   │   │   └── (待添加组件)
│   │   │
│   │   ├── services/                  # 服务层
│   │   │   ├── http.js                # HTTP 请求服务
│   │   │   └── api.js                 # API 接口封装
│   │   │
│   │   ├── utils/                     # 工具函数
│   │   │   ├── i18n.js                # 国际化工具
│   │   │   └── theme.js               # 主题工具
│   │   │
│   │   └── assets/                    # 静态资源
│   │       ├── styles/                # 样式文件
│   │       │   └── themes.css         # 主题样式
│   │       └── locales/               # 语言文件
│   │           ├── zh-CN.json         # 中文语言包
│   │           └── en-US.json         # 英文语言包
│   │
│   └── shared/                        # 共享模块
│       ├── config/                    # 配置文件
│       │   └── api.js                 # API 配置
│       ├── constants/                 # 常量定义
│       │   └── index.js               # 应用常量
│       └── types/                     # 类型定义
│           └── (待添加类型定义)
│
├── package.json                       # 项目配置
└── PROJECT_STRUCTURE.md              # 本文档
```

## 🔧 核心功能模块

### 1. API 配置模块 (`src/shared/config/api.js`)

**功能**：
- 统一管理 API 基础配置
- 支持多环境配置（开发、测试、生产）
- 提供 API 端点定义
- 支持动态切换 API 域名

**特性**：
```javascript
// 环境配置
const API_CONFIG = {
  development: {
    BASE_URL: 'http://localhost:3000',
    TIMEOUT: 10000,
    RETRY_TIMES: 3
  },
  production: {
    BASE_URL: 'https://api.aitools.com',
    TIMEOUT: 15000,
    RETRY_TIMES: 2
  }
};

// API 端点配置
const API_ENDPOINTS = {
  BRANDS: {
    LIST: '/api/brands',
    DETAIL: (id) => `/api/brands/${id}`,
    CREATE: '/api/brands'
  },
  USERS: {
    LIST: '/api/users',
    AUTH: '/api/users/auth'
  }
};
```

### 2. HTTP 服务模块 (`src/renderer/services/http.js`)

**功能**：
- 统一的 HTTP 请求处理
- 自动重试机制
- 请求超时控制
- 错误处理和状态管理

**特性**：
- 支持 GET、POST、PUT、DELETE 等方法
- 自动 JSON 序列化/反序列化
- 请求拦截和响应拦截
- 网络错误自动重试

### 3. API 服务模块 (`src/renderer/services/api.js`)

**功能**：
- 封装所有 API 调用
- 提供统一的接口规范
- 业务逻辑与网络请求分离

**特性**：
```javascript
// 品牌管理 API
await apiService.getBrands({ active: true });
await apiService.createBrand(brandData);
await apiService.updateBrand(id, brandData);

// 用户管理 API
await apiService.authenticateUser(authCode);
await apiService.getUsers({ userType: 'admin' });
```

### 4. 国际化工具 (`src/renderer/utils/i18n.js`)

**功能**：
- 统一的多语言处理
- 嵌套键值查找
- 动态语言切换
- 文本参数替换

**特性**：
```javascript
// 嵌套键查找
const text = getNestedValue(i18nTexts, 'admin.table.brand_name');

// 参数替换
const message = formatText('欢迎，{username}！', { username: 'admin' });

// 语言切换
await switchLanguage('en-US');
```

### 5. 主题工具 (`src/renderer/utils/theme.js`)

**功能**：
- 统一的主题管理
- 支持浅色/深色/自动主题
- 主题实时切换
- 系统主题跟随

**特性**：
```javascript
// 主题切换
await switchTheme('dark');

// 自动加载主题
await loadAndApplyTheme();

// 监听主题变化
onThemeChange((theme) => {
  console.log('主题已切换:', theme);
});
```

## 📋 配置说明

### API 域名配置

**开发环境**：
- 默认：`http://localhost:3000`
- 可通过环境变量 `NODE_ENV` 控制

**生产环境**：
- 默认：`https://api.aitools.com`
- 可在 `src/shared/config/api.js` 中修改

**动态配置**：
```javascript
// 运行时切换 API 域名
apiService.setBaseUrl('https://custom-api.example.com');

// 检查连接状态
const status = await apiService.checkConnection();
```

### 环境变量支持

```javascript
// 通过环境变量配置
process.env.NODE_ENV = 'production';
process.env.API_BASE_URL = 'https://custom-api.com';
```

## 🚀 使用指南

### 1. 添加新的 API 接口

1. 在 `src/shared/config/api.js` 中添加端点定义
2. 在 `src/renderer/services/api.js` 中添加接口方法
3. 在页面中调用 API 服务

```javascript
// 1. 添加端点
API_ENDPOINTS.SETTINGS = {
  GET: '/api/settings',
  UPDATE: '/api/settings'
};

// 2. 添加服务方法
async getSettings() {
  return this.http.get(API_ENDPOINTS.SETTINGS.GET);
}

// 3. 页面中使用
const settings = await apiService.getSettings();
```

### 2. 添加新的工具函数

在 `src/renderer/utils/` 目录下创建新的工具模块：

```javascript
// src/renderer/utils/validation.js
function validateEmail(email) {
  const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return pattern.test(email);
}

module.exports = { validateEmail };
```

### 3. 添加新的常量

在 `src/shared/constants/index.js` 中添加：

```javascript
const NEW_CONSTANTS = {
  FEATURE_A: 'feature-a',
  FEATURE_B: 'feature-b'
};

module.exports = {
  // ... 其他常量
  NEW_CONSTANTS
};
```

## 📊 重构效果

### 代码组织改进
- **模块化**：功能按职责清晰分离
- **可维护性**：代码结构更加清晰
- **可扩展性**：易于添加新功能和模块

### API 管理改进
- **统一配置**：所有 API 配置集中管理
- **环境支持**：支持多环境配置
- **错误处理**：统一的错误处理机制
- **重试机制**：网络请求自动重试

### 开发体验改进
- **类型安全**：常量定义避免魔法字符串
- **工具函数**：常用功能封装为工具函数
- **配置灵活**：支持运行时配置修改

## 🔄 迁移指南

### 从旧结构迁移

1. **更新引用路径**：
   - 主题样式：`themes.css` → `../assets/styles/themes.css`
   - 语言文件：`locales/` → `../assets/locales/`

2. **使用新的 API 服务**：
   ```javascript
   // 旧方式
   const response = await fetch('/api/brands');
   
   // 新方式
   const result = await apiService.getBrands();
   ```

3. **使用新的工具函数**：
   ```javascript
   // 旧方式
   function getNestedValue(obj, key) { /* ... */ }
   
   // 新方式
   const { getNestedValue } = require('../utils/i18n');
   ```

## 🎯 后续优化建议

1. **组件化**：将页面拆分为可复用组件
2. **状态管理**：引入状态管理方案
3. **测试覆盖**：添加单元测试和集成测试
4. **构建优化**：优化打包和构建流程
5. **类型定义**：添加 TypeScript 支持

现在前端项目具有现代化的结构，支持灵活的 API 配置，代码组织更加清晰，便于维护和扩展。
