/**
 * 文件管理路由
 * 处理文件上传、下载、删除等操作的路由定义
 */

const express = require('express');
const router = express.Router();
const fileController = require('../controllers/fileController');
const { requireAuth, requireAdmin } = require('../middleware/auth');
const { upload, uploadImage, processImage, handleUploadError } = require('../middleware/upload');

/**
 * @route GET /api/files/test-auth
 * @desc 测试认证是否工作
 * @access Private
 */
router.get('/test-auth', requireAuth, (req, res) => {
  console.log('=== 认证测试 ===');
  console.log('req.user:', req.user);
  console.log('req.headers.authorization:', req.headers.authorization);

  res.json({
    status: 'success',
    message: '认证成功',
    user: req.user
  });
});

/**
 * @route POST /api/files/test-upload
 * @desc 测试文件上传（无认证）
 * @access Public
 */
router.post('/test-upload', uploadImage.single('image'), processImage, (req, res) => {
  console.log('=== 测试文件上传 ===');
  console.log('req.file:', req.file);
  console.log('req.body:', req.body);

  if (!req.file) {
    return res.status(400).json({
      status: 'error',
      message: '没有上传文件'
    });
  }

  res.json({
    status: 'success',
    message: '文件上传成功',
    data: {
      originalName: req.file.originalname,
      filename: req.file.filename,
      size: req.file.size,
      mimetype: req.file.mimetype,
      path: req.file.path,
      url: `/uploads/${req.file.filename}`
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * @route POST /api/files/upload
 * @desc 上传单个文件
 * @access Private
 * @body {file} file - 要上传的文件
 * @body {string} description - 文件描述
 * @body {string} tags - 标签（逗号分隔）
 * @body {string} category - 分类
 * @body {boolean} isPublic - 是否公开
 * @body {string} relatedModel - 关联模型名称
 * @body {string} relatedId - 关联对象ID
 */
router.post('/upload', 
  requireAuth, 
  upload.single('file'), 
  fileController.uploadFile,
  handleUploadError
);

/**
 * @route POST /api/files/upload-image
 * @desc 上传并处理图片文件
 * @access Private
 * @body {file} image - 要上传的图片文件
 * @body {string} description - 文件描述
 * @body {string} tags - 标签（逗号分隔）
 * @body {string} category - 分类
 * @body {boolean} isPublic - 是否公开
 * @body {string} relatedModel - 关联模型名称
 * @body {string} relatedId - 关联对象ID
 */
router.post('/upload-image', 
  requireAuth, 
  uploadImage.single('image'), 
  processImage,
  fileController.uploadFile,
  handleUploadError
);

/**
 * @route POST /api/files/upload-multiple
 * @desc 批量上传文件
 * @access Private
 * @body {file[]} files - 要上传的文件数组
 * @body {string} description - 文件描述
 * @body {string} tags - 标签（逗号分隔）
 * @body {string} category - 分类
 * @body {boolean} isPublic - 是否公开
 */
router.post('/upload-multiple', 
  requireAuth, 
  upload.array('files', 5), 
  fileController.uploadFile,
  handleUploadError
);

/**
 * @route GET /api/files
 * @desc 获取文件列表
 * @access Private
 * @query {number} page - 页码
 * @query {number} limit - 每页数量
 * @query {string} fileType - 文件类型过滤
 * @query {string} status - 文件状态过滤
 * @query {string} search - 搜索关键词
 * @query {string} uploadedBy - 上传者ID过滤
 * @query {string} tags - 标签过滤（逗号分隔）
 * @query {string} sortBy - 排序字段
 * @query {string} sortOrder - 排序方向 (asc/desc)
 */
router.get('/', requireAuth, fileController.getFiles);

/**
 * @route GET /api/files/stats
 * @desc 获取文件统计信息
 * @access Private (Admin)
 */
router.get('/stats', requireAuth, requireAdmin, fileController.getFileStats);

/**
 * @route GET /api/files/:id
 * @desc 根据ID获取文件详情
 * @access Private
 * @param {string} id - 文件ID
 */
router.get('/:id', requireAuth, fileController.getFileById);

/**
 * @route GET /api/files/:id/download
 * @desc 下载文件
 * @access Private
 * @param {string} id - 文件ID
 */
router.get('/:id/download', requireAuth, fileController.downloadFile);

/**
 * @route PUT /api/files/:id
 * @desc 更新文件信息
 * @access Private
 * @param {string} id - 文件ID
 * @body {string} description - 文件描述
 * @body {string} tags - 标签（逗号分隔）
 * @body {string} category - 分类
 * @body {boolean} isPublic - 是否公开
 */
router.put('/:id', requireAuth, fileController.updateFile);

/**
 * @route DELETE /api/files/:id
 * @desc 删除文件
 * @access Private
 * @param {string} id - 文件ID
 */
router.delete('/:id', requireAuth, fileController.deleteFile);

/**
 * @route POST /api/files/:id/restore
 * @desc 恢复已删除的文件
 * @access Private (Admin)
 * @param {string} id - 文件ID
 */
router.post('/:id/restore', requireAuth, requireAdmin, fileController.restoreFile);

/**
 * @route DELETE /api/files/batch
 * @desc 批量删除文件
 * @access Private
 * @body {string[]} fileIds - 要删除的文件ID数组
 */
router.delete('/batch', requireAuth, fileController.batchDeleteFiles);



/**
 * @route POST /api/files/cleanup-duplicates
 * @desc 清理重复文件
 * @access Private (Admin only)
 */
router.post('/cleanup-duplicates', requireAuth, requireAdmin, fileController.cleanupDuplicateFiles);

module.exports = router;
