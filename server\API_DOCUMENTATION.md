# AI重器后端 API 接口文档

## 📋 概述

AI重器后端服务提供品牌管理和用户管理的 RESTful API 接口。

**服务地址**: `http://localhost:3000`  
**API 基础路径**: `/api`

## 🔧 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "count": 0  // 仅列表接口返回
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误描述",
  "message": "详细错误信息"
}
```

## 🏷️ 品牌管理 API

### 1. 获取品牌列表
- **接口**: `GET /api/brands`
- **描述**: 获取所有品牌列表
- **查询参数**:
  - `active` (可选): `true` - 仅获取活跃品牌

**请求示例**:
```
GET /api/brands
GET /api/brands?active=true
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "_id": "64f1234567890abcdef12345",
      "name": "AI重器",
      "icon": "https://example.com/logo.png",
      "description": "AI重器 - 跨平台的AI工具集合",
      "isActive": true,
      "sortOrder": 1,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "count": 1
}
```

### 2. 获取单个品牌
- **接口**: `GET /api/brands/:id`
- **描述**: 根据ID获取单个品牌信息

**请求示例**:
```
GET /api/brands/64f1234567890abcdef12345
```

### 3. 创建品牌
- **接口**: `POST /api/brands`
- **描述**: 创建新品牌

**请求体**:
```json
{
  "name": "品牌名称",
  "icon": "图标URL",
  "description": "品牌描述",
  "isActive": true,
  "sortOrder": 1
}
```

### 4. 更新品牌
- **接口**: `PUT /api/brands/:id`
- **描述**: 更新指定品牌信息

### 5. 删除品牌
- **接口**: `DELETE /api/brands/:id`
- **描述**: 删除指定品牌

## 👥 用户管理 API

### 1. 获取用户列表
- **接口**: `GET /api/users`
- **描述**: 获取所有用户列表
- **查询参数**:
  - `active` (可选): `true` - 仅获取活跃用户
  - `userType` (可选): `user` | `admin` - 按用户类型筛选

**请求示例**:
```
GET /api/users
GET /api/users?active=true
GET /api/users?userType=admin
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "_id": "64f1234567890abcdef12345",
      "username": "admin",
      "email": "<EMAIL>",
      "displayName": "系统管理员",
      "userType": "admin",
      "userTypeDisplay": "管理员",
      "isActive": true,
      "loginCount": 5,
      "lastLoginAt": "2024-01-01T00:00:00.000Z",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "count": 1
}
```

### 2. 获取单个用户
- **接口**: `GET /api/users/:id`
- **描述**: 根据ID获取单个用户信息

### 3. 创建用户
- **接口**: `POST /api/users`
- **描述**: 创建新用户

**请求体**:
```json
{
  "username": "用户名",
  "email": "邮箱地址",
  "authCode": "授权码",
  "userType": "user",
  "displayName": "显示名称",
  "description": "用户描述",
  "isActive": true
}
```

### 4. 更新用户
- **接口**: `PUT /api/users/:id`
- **描述**: 更新指定用户信息

### 5. 删除用户
- **接口**: `DELETE /api/users/:id`
- **描述**: 删除指定用户

### 6. 用户授权验证
- **接口**: `POST /api/users/auth`
- **描述**: 验证用户授权码

**请求体**:
```json
{
  "authCode": "ADMIN2024"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user": {
      "_id": "64f1234567890abcdef12345",
      "username": "admin",
      "userType": "admin",
      "displayName": "系统管理员"
    },
    "message": "授权验证成功"
  }
}
```

### 7. 重新生成授权码
- **接口**: `POST /api/users/:id/regenerate-auth`
- **描述**: 为指定用户重新生成授权码

## 🔍 系统信息 API

### 根路由
- **接口**: `GET /`
- **描述**: 获取服务基本信息

**响应示例**:
```json
{
  "message": "AI重器后端服务",
  "version": "1.0.0",
  "status": "running"
}
```

## 📊 数据模型

### Brand 模型
```javascript
{
  name: String,           // 品牌名称 (必填)
  icon: String,           // 图标URL (必填)
  description: String,    // 描述
  isActive: Boolean,      // 是否活跃 (默认: true)
  sortOrder: Number,      // 排序 (默认: 0)
  createdAt: Date,        // 创建时间
  updatedAt: Date         // 更新时间
}
```

### User 模型
```javascript
{
  username: String,       // 用户名 (必填, 唯一)
  email: String,          // 邮箱 (必填, 唯一)
  authCode: String,       // 授权码 (必填, 唯一)
  userType: String,       // 用户类型: 'user' | 'admin' (默认: 'user')
  displayName: String,    // 显示名称
  description: String,    // 描述
  isActive: Boolean,      // 是否活跃 (默认: true)
  loginCount: Number,     // 登录次数 (默认: 0)
  lastLoginAt: Date,      // 最后登录时间
  createdAt: Date,        // 创建时间
  updatedAt: Date         // 更新时间
}
```

## ⚠️ 错误代码

- `400` - 请求参数错误
- `404` - 资源未找到
- `500` - 服务器内部错误

## 🔒 安全说明

- 所有用户相关接口返回的数据已过滤敏感信息
- 授权码用于客户端验证，请妥善保管
- 建议在生产环境中添加适当的身份验证和授权机制

## 🚀 部署说明

1. 安装依赖: `npm install`
2. 配置环境变量: 复制 `.env.example` 为 `.env`
3. 启动服务: `npm start`
4. 服务将运行在 `http://localhost:3000`

## 📝 更新日志

- **v1.0.0** - 初始版本，提供基础的品牌和用户管理 API
