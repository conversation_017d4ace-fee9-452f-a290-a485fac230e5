/**
 * HTTP 请求服务
 * 统一处理 API 请求，包括错误处理、重试机制等
 */

const { config, API_ENDPOINTS, HTTP_STATUS, DEFAULT_HEADERS } = require('../../shared/config/api');

class HttpService {
  constructor() {
    this.baseUrl = config.BASE_URL;
    this.timeout = config.TIMEOUT;
    this.retryTimes = config.RETRY_TIMES;
    this.defaultHeaders = { ...DEFAULT_HEADERS };
  }

  /**
   * 发送 HTTP 请求
   * @param {string} url - 请求 URL
   * @param {object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async request(url, options = {}) {
    const {
      method = 'GET',
      headers = {},
      body = null,
      timeout = this.timeout,
      retries = this.retryTimes
    } = options;

    // 构建完整 URL
    const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;

    // 合并请求头
    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers
    };

    // 构建请求配置
    const requestConfig = {
      method,
      headers: requestHeaders
    };

    // 添加请求体（如果有）
    if (body && method !== 'GET') {
      requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
    }

    // 执行请求（带重试机制）
    return this._executeWithRetry(fullUrl, requestConfig, retries);
  }

  /**
   * 带重试机制的请求执行
   * @param {string} url - 请求 URL
   * @param {object} config - 请求配置
   * @param {number} retries - 重试次数
   * @returns {Promise} 请求结果
   */
  async _executeWithRetry(url, config, retries) {
    try {
      const response = await this._fetchWithTimeout(url, config);
      
      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 解析响应数据
      const contentType = response.headers.get('content-type');
      let data;
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      return {
        success: true,
        data,
        status: response.status,
        headers: response.headers
      };

    } catch (error) {
      console.error(`请求失败: ${url}`, error);

      // 如果还有重试次数，则重试
      if (retries > 0) {
        console.log(`重试请求: ${url}, 剩余重试次数: ${retries}`);
        await this._delay(1000); // 延迟 1 秒后重试
        return this._executeWithRetry(url, config, retries - 1);
      }

      // 返回错误结果
      return {
        success: false,
        error: error.message,
        status: error.status || 0
      };
    }
  }

  /**
   * 带超时的 fetch 请求
   * @param {string} url - 请求 URL
   * @param {object} config - 请求配置
   * @returns {Promise} fetch 结果
   */
  async _fetchWithTimeout(url, config) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('请求超时');
      }
      throw error;
    }
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} 延迟 Promise
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // HTTP 方法快捷方式
  async get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' });
  }

  async post(url, data, options = {}) {
    return this.request(url, { ...options, method: 'POST', body: data });
  }

  async put(url, data, options = {}) {
    return this.request(url, { ...options, method: 'PUT', body: data });
  }

  async delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' });
  }

  async patch(url, data, options = {}) {
    return this.request(url, { ...options, method: 'PATCH', body: data });
  }

  /**
   * 设置默认请求头
   * @param {object} headers - 请求头对象
   */
  setDefaultHeaders(headers) {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers };
  }

  /**
   * 设置基础 URL
   * @param {string} baseUrl - 基础 URL
   */
  setBaseUrl(baseUrl) {
    this.baseUrl = baseUrl;
  }

  /**
   * 获取当前配置
   * @returns {object} 当前配置
   */
  getConfig() {
    return {
      baseUrl: this.baseUrl,
      timeout: this.timeout,
      retryTimes: this.retryTimes,
      defaultHeaders: this.defaultHeaders
    };
  }
}

// 创建单例实例
const httpService = new HttpService();

module.exports = httpService;
