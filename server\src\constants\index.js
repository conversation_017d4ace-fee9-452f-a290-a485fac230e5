/**
 * 应用常量定义
 * 统一管理应用中使用的常量
 */

// HTTP 状态码
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
};

// 响应状态
const RESPONSE_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  FAIL: 'fail'
};

// 用户类型
const USER_TYPES = {
  USER: 'user',
  ADMIN: 'admin'
};

// 用户类型显示名称
const USER_TYPE_NAMES = {
  [USER_TYPES.USER]: '普通用户',
  [USER_TYPES.ADMIN]: '管理员'
};

// 品牌状态
const BRAND_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  DELETED: 'deleted'
};

// 品牌状态显示名称
const BRAND_STATUS_NAMES = {
  [BRAND_STATUS.ACTIVE]: '活跃',
  [BRAND_STATUS.INACTIVE]: '非活跃',
  [BRAND_STATUS.PENDING]: '待审核',
  [BRAND_STATUS.DELETED]: '已删除'
};

// 验证规则
const VALIDATION_RULES = {
  BRAND: {
    NAME: {
      MIN_LENGTH: 1,
      MAX_LENGTH: 100,
      PATTERN: /^[\u4e00-\u9fa5a-zA-Z0-9\s\-_]+$/
    },
    DESCRIPTION: {
      MAX_LENGTH: 500
    },
    URL: {
      PATTERN: /^https?:\/\/.+/
    }
  },
  
  USER: {
    USERNAME: {
      MIN_LENGTH: 3,
      MAX_LENGTH: 20,
      PATTERN: /^[a-zA-Z0-9_-]+$/
    },
    EMAIL: {
      PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    },
    AUTH_CODE: {
      MIN_LENGTH: 6,
      MAX_LENGTH: 50
    }
  },
  
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
    MAX_LIMIT: 100
  }
};

// 错误代码
const ERROR_CODES = {
  // 通用错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  
  // 数据库错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  DUPLICATE_KEY: 'DUPLICATE_KEY',
  
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  INVALID_TOKEN: 'INVALID_TOKEN',
  
  // 业务错误
  BRAND_NOT_FOUND: 'BRAND_NOT_FOUND',
  BRAND_ALREADY_EXISTS: 'BRAND_ALREADY_EXISTS',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  INVALID_AUTH_CODE: 'INVALID_AUTH_CODE'
};

// 错误消息
const ERROR_MESSAGES = {
  [ERROR_CODES.VALIDATION_ERROR]: '数据验证失败',
  [ERROR_CODES.NOT_FOUND]: '资源未找到',
  [ERROR_CODES.INTERNAL_ERROR]: '服务器内部错误',
  [ERROR_CODES.DATABASE_ERROR]: '数据库操作失败',
  [ERROR_CODES.DUPLICATE_KEY]: '数据已存在',
  [ERROR_CODES.UNAUTHORIZED]: '未授权访问',
  [ERROR_CODES.FORBIDDEN]: '权限不足',
  [ERROR_CODES.INVALID_TOKEN]: '无效的令牌',
  [ERROR_CODES.BRAND_NOT_FOUND]: '品牌未找到',
  [ERROR_CODES.BRAND_ALREADY_EXISTS]: '品牌已存在',
  [ERROR_CODES.USER_NOT_FOUND]: '用户未找到',
  [ERROR_CODES.INVALID_AUTH_CODE]: '无效的认证码'
};

// 成功消息
const SUCCESS_MESSAGES = {
  CREATED: '创建成功',
  UPDATED: '更新成功',
  DELETED: '删除成功',
  RETRIEVED: '获取成功'
};

// 日志级别
const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
};

// 缓存键前缀
const CACHE_KEYS = {
  BRAND: 'brand:',
  USER: 'user:',
  BRANDS_LIST: 'brands:list:',
  USERS_LIST: 'users:list:'
};

// 缓存过期时间（秒）
const CACHE_TTL = {
  SHORT: 300,    // 5 分钟
  MEDIUM: 1800,  // 30 分钟
  LONG: 3600,    // 1 小时
  VERY_LONG: 86400 // 24 小时
};

// API 限制
const API_LIMITS = {
  BRANDS: {
    MAX_PER_PAGE: 100,
    DEFAULT_PER_PAGE: 10
  },
  USERS: {
    MAX_PER_PAGE: 50,
    DEFAULT_PER_PAGE: 10
  }
};

// 文件上传限制
const UPLOAD_LIMITS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf'
  ],
  MAX_FILES: 5
};

// 数据库集合名称
const COLLECTIONS = {
  BRANDS: 'brands',
  USERS: 'users',
  LOGS: 'logs'
};

// 环境类型
const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  TEST: 'test',
  PRODUCTION: 'production'
};

// 请求方法
const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
  OPTIONS: 'OPTIONS'
};

// 内容类型
const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM: 'application/x-www-form-urlencoded',
  MULTIPART: 'multipart/form-data',
  TEXT: 'text/plain',
  HTML: 'text/html',
  XML: 'application/xml'
};

// 排序方向
const SORT_DIRECTIONS = {
  ASC: 1,
  DESC: -1
};

// 默认排序字段
const DEFAULT_SORT = {
  BRANDS: { createdAt: SORT_DIRECTIONS.DESC },
  USERS: { createdAt: SORT_DIRECTIONS.DESC }
};

// 时间格式
const DATE_FORMATS = {
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss'
};

// 正则表达式
const REGEX_PATTERNS = {
  OBJECT_ID: /^[0-9a-fA-F]{24}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/.+/,
  PHONE: /^1[3-9]\d{9}$/,
  USERNAME: /^[a-zA-Z0-9_-]{3,20}$/
};

module.exports = {
  HTTP_STATUS,
  RESPONSE_STATUS,
  USER_TYPES,
  USER_TYPE_NAMES,
  BRAND_STATUS,
  BRAND_STATUS_NAMES,
  VALIDATION_RULES,
  ERROR_CODES,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  LOG_LEVELS,
  CACHE_KEYS,
  CACHE_TTL,
  API_LIMITS,
  UPLOAD_LIMITS,
  COLLECTIONS,
  ENVIRONMENTS,
  HTTP_METHODS,
  CONTENT_TYPES,
  SORT_DIRECTIONS,
  DEFAULT_SORT,
  DATE_FORMATS,
  REGEX_PATTERNS
};
