const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// 国际化文本
let i18nTexts = {};

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {
    // 加载国际化文本
    await loadI18nTexts();

    // 加载并应用主题
    await loadAndApplyTheme();

    const form = document.getElementById('loginForm');
    const authCodeInput = document.getElementById('authCode');
    const loginBtn = document.getElementById('loginBtn');
    const loading = document.getElementById('loading');
    const errorMessage = document.getElementById('errorMessage');

    // 表单提交事件
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        await handleLogin();
    });

    // 回车键提交
    authCodeInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleLogin();
        }
    });

    // 输入框变化时清除错误信息
    authCodeInput.addEventListener('input', () => {
        hideError();
    });

    // 处理登录
    async function handleLogin() {
        const authCode = authCodeInput.value.trim();
        
        if (!authCode) {
            showError(getNestedValue(i18nTexts, 'login.error.empty') || '请输入授权码');
            return;
        }

        try {
            showLoading(true);
            hideError();

            console.log('前端：开始验证授权码:', authCode);

            // 通过IPC发送授权验证请求
            const result = await ipcRenderer.invoke('verify-auth-code', authCode);

            console.log('前端：收到验证结果:', result);

            if (result.success) {
                console.log('前端：验证成功，切换到主界面');
                // 验证成功，通知主进程切换到主界面
                await ipcRenderer.invoke('auth-success', result.user);
            } else {
                console.log('前端：验证失败:', result.error);
                showError(result.error || getNestedValue(i18nTexts, 'login.error.invalid') || '授权验证失败');
            }
        } catch (error) {
            console.error('授权验证错误:', error);
            showError(getNestedValue(i18nTexts, 'login.error.network') || '网络连接失败，请检查后端服务是否运行');
        } finally {
            showLoading(false);
        }
    }

    // 显示加载状态
    function showLoading(show) {
        if (show) {
            loading.classList.add('show');
            loginBtn.disabled = true;
            loginBtn.textContent = getNestedValue(i18nTexts, 'login.verifying') || '验证中...';
        } else {
            loading.classList.remove('show');
            loginBtn.disabled = false;
            loginBtn.textContent = getNestedValue(i18nTexts, 'login.verify') || '验证授权码';
        }
    }

    // 显示错误信息
    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            hideError();
        }, 3000);
    }

    // 隐藏错误信息
    function hideError() {
        errorMessage.style.display = 'none';
    }

    // 监听主进程的消息
    ipcRenderer.on('auth-error', (event, message) => {
        showError(message);
        showLoading(false);
    });

    // 自动聚焦到输入框
    authCodeInput.focus();
});

// 加载国际化文本
async function loadI18nTexts() {
    try {
        const language = await ipcRenderer.invoke('get-setting', 'language') || 'zh-CN';
        i18nTexts = await ipcRenderer.invoke('get-i18n-texts', language);
        updateI18nTexts();
    } catch (error) {
        console.error('加载国际化文本失败:', error);
    }
}

// 更新界面文本
function updateI18nTexts() {
    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        const text = getNestedValue(i18nTexts, key);
        if (text) {
            element.textContent = text;
        }
    });

    // 更新 placeholder
    document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
        const key = element.getAttribute('data-i18n-placeholder');
        const text = getNestedValue(i18nTexts, key);
        if (text) {
            element.placeholder = text;
        }
    });
}

// 获取嵌套对象的值
function getNestedValue(obj, key) {
    return key.split('.').reduce((current, keyPart) => {
        return current && current[keyPart] !== undefined ? current[keyPart] : undefined;
    }, obj);
}

// 加载并应用主题
async function loadAndApplyTheme() {
    try {
        const theme = await ipcRenderer.invoke('get-setting', 'theme') || 'auto';
        applyTheme(theme);
    } catch (error) {
        console.error('加载主题失败:', error);
        applyTheme('auto');
    }
}

// 应用主题
function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    console.log('登录页面主题已应用:', theme);
}

// 监听主题更改事件
ipcRenderer.on('theme-changed', (event, theme) => {
    console.log('登录页面收到主题更改通知:', theme);
    applyTheme(theme);
});

// 监听语言更改事件
ipcRenderer.on('language-changed', async (event, newLanguage) => {
    console.log('登录页面收到语言更改通知:', newLanguage);
    await loadI18nTexts();
});

// 添加一些交互效果
document.addEventListener('DOMContentLoaded', () => {
    const loginBtn = document.getElementById('loginBtn');
    
    loginBtn.addEventListener('mouseenter', () => {
        if (!loginBtn.disabled) {
            loginBtn.style.transform = 'translateY(-2px)';
        }
    });
    
    loginBtn.addEventListener('mouseleave', () => {
        if (!loginBtn.disabled) {
            loginBtn.style.transform = 'translateY(0)';
        }
    });
});
