/**
 * 用户路由
 * 定义用户相关的API路由
 */

const express = require('express');
const userController = require('../controllers/userController');
const { validateRequest } = require('../middleware/validation');
const { requireAuth, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * @route POST /api/users/auth
 * @desc 用户认证
 * @access Public
 * @body {string} authCode - 认证码
 */
router.post('/auth', 
  validateRequest(['authCode']),
  userController.authenticate
);

/**
 * @route GET /api/users/me
 * @desc 获取当前用户信息
 * @access Private
 */
router.get('/me', 
  requireAuth,
  userController.getCurrentUser
);

/**
 * @route PUT /api/users/me
 * @desc 更新当前用户信息
 * @access Private
 * @body {string} displayName - 显示名称
 * @body {string} avatar - 头像URL
 * @body {string} description - 个人描述
 * @body {object} preferences - 偏好设置
 */
router.put('/me', 
  requireAuth,
  userController.updateCurrentUser
);

/**
 * @route GET /api/users
 * @desc 获取用户列表
 * @access Private (Admin)
 * @query {number} page - 页码
 * @query {number} limit - 每页数量
 * @query {string} userType - 用户类型
 * @query {boolean} isActive - 是否活跃
 * @query {string} search - 搜索关键词
 * @query {string} sortBy - 排序字段
 * @query {string} sortOrder - 排序方向
 */
router.get('/', 
  requireAuth, 
  requireAdmin,
  userController.getUsers
);

/**
 * @route GET /api/users/active
 * @desc 获取活跃用户列表
 * @access Private (Admin)
 * @query {number} limit - 限制数量
 * @query {string} userType - 用户类型
 */
router.get('/active', 
  requireAuth, 
  requireAdmin,
  userController.getActiveUsers
);

/**
 * @route GET /api/users/admins
 * @desc 获取管理员用户列表
 * @access Private (Admin)
 * @query {number} limit - 限制数量
 * @query {number} skip - 跳过数量
 */
router.get('/admins', 
  requireAuth, 
  requireAdmin,
  userController.getAdminUsers
);

/**
 * @route GET /api/users/online
 * @desc 获取在线用户列表
 * @access Private (Admin)
 */
router.get('/online', 
  requireAuth, 
  requireAdmin,
  userController.getOnlineUsers
);

/**
 * @route GET /api/users/search
 * @desc 搜索用户
 * @access Private (Admin)
 * @query {string} keyword - 搜索关键词
 * @query {string} userType - 用户类型
 * @query {boolean} isActive - 是否活跃
 * @query {number} limit - 限制数量
 * @query {number} skip - 跳过数量
 */
router.get('/search', 
  requireAuth, 
  requireAdmin,
  userController.searchUsers
);

/**
 * @route GET /api/users/stats
 * @desc 获取用户统计信息
 * @access Private (Admin)
 */
router.get('/stats', 
  requireAuth, 
  requireAdmin,
  userController.getUserStats
);

/**
 * @route GET /api/users/:id
 * @desc 根据ID获取用户
 * @access Private
 * @param {string} id - 用户ID
 */
router.get('/:id', 
  requireAuth,
  userController.getUserById
);

/**
 * @route POST /api/users
 * @desc 创建用户
 * @access Private (Admin)
 * @body {string} username - 用户名
 * @body {string} email - 邮箱
 * @body {string} authCode - 认证码
 * @body {string} userType - 用户类型
 * @body {string} displayName - 显示名称
 * @body {string} description - 个人描述
 */
router.post('/', 
  requireAuth, 
  requireAdmin,
  validateRequest(['username']),
  userController.createUser
);

/**
 * @route PUT /api/users/:id
 * @desc 更新用户
 * @access Private (Admin or Self)
 * @param {string} id - 用户ID
 * @body {string} username - 用户名
 * @body {string} email - 邮箱
 * @body {string} displayName - 显示名称
 * @body {string} description - 个人描述
 * @body {string} userType - 用户类型 (Admin only)
 * @body {boolean} isActive - 是否活跃 (Admin only)
 */
router.put('/:id', 
  requireAuth,
  userController.updateUser
);

/**
 * @route DELETE /api/users/:id
 * @desc 删除用户
 * @access Private (Admin)
 * @param {string} id - 用户ID
 */
router.delete('/:id', 
  requireAuth, 
  requireAdmin,
  userController.deleteUser
);

/**
 * @route POST /api/users/:id/reset-auth-code
 * @desc 重置用户认证码
 * @access Private (Admin)
 * @param {string} id - 用户ID
 */
router.post('/:id/reset-auth-code', 
  requireAuth, 
  requireAdmin,
  userController.resetAuthCode
);

/**
 * @route POST /api/users/batch/status
 * @desc 批量更新用户状态
 * @access Private (Admin)
 * @body {array} ids - 用户ID数组
 * @body {boolean} isActive - 新的活跃状态
 */
router.post('/batch/status', 
  requireAuth, 
  requireAdmin,
  validateRequest(['ids', 'isActive']),
  userController.batchUpdateStatus
);

/**
 * @route PUT /api/users/:id/preferences
 * @desc 更新用户偏好设置
 * @access Private (Self or Admin)
 * @param {string} id - 用户ID
 * @body {object} preferences - 偏好设置
 */
router.put('/:id/preferences', 
  requireAuth,
  validateRequest(['preferences']),
  userController.updateUserPreferences
);

/**
 * @route POST /api/users/:id/activate
 * @desc 激活用户
 * @access Private (Admin)
 * @param {string} id - 用户ID
 */
router.post('/:id/activate', 
  requireAuth, 
  requireAdmin,
  userController.activateUser
);

/**
 * @route POST /api/users/:id/deactivate
 * @desc 停用用户
 * @access Private (Admin)
 * @param {string} id - 用户ID
 */
router.post('/:id/deactivate', 
  requireAuth, 
  requireAdmin,
  userController.deactivateUser
);

/**
 * @route GET /api/users/:id/login-history
 * @desc 获取用户登录历史
 * @access Private (Self or Admin)
 * @param {string} id - 用户ID
 */
router.get('/:id/login-history', 
  requireAuth,
  userController.getUserLoginHistory
);

/**
 * @route DELETE /api/users/:id/login-history
 * @desc 清除用户登录历史
 * @access Private (Self or Admin)
 * @param {string} id - 用户ID
 */
router.delete('/:id/login-history', 
  requireAuth,
  userController.clearUserLoginHistory
);

module.exports = router;
