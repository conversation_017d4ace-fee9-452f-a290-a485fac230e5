/**
 * 验证工具函数
 * 统一处理数据验证
 */

const { VALIDATION_RULES, REGEX_PATTERNS } = require('../constants');

/**
 * 验证品牌名称
 * @param {string} name - 品牌名称
 * @returns {Object} 验证结果
 */
function validateBrandName(name) {
  const errors = [];
  
  if (!name || typeof name !== 'string') {
    errors.push('品牌名称不能为空');
  } else {
    const trimmedName = name.trim();
    
    if (trimmedName.length < VALIDATION_RULES.BRAND.NAME.MIN_LENGTH) {
      errors.push(`品牌名称至少需要 ${VALIDATION_RULES.BRAND.NAME.MIN_LENGTH} 个字符`);
    }
    
    if (trimmedName.length > VALIDATION_RULES.BRAND.NAME.MAX_LENGTH) {
      errors.push(`品牌名称不能超过 ${VALIDATION_RULES.BRAND.NAME.MAX_LENGTH} 个字符`);
    }
    
    if (!VALIDATION_RULES.BRAND.NAME.PATTERN.test(trimmedName)) {
      errors.push('品牌名称只能包含中文、英文、数字、空格、连字符和下划线');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: name ? name.trim() : null
  };
}

/**
 * 验证品牌描述
 * @param {string} description - 品牌描述
 * @returns {Object} 验证结果
 */
function validateBrandDescription(description) {
  const errors = [];
  
  if (description && typeof description === 'string') {
    const trimmedDescription = description.trim();
    
    if (trimmedDescription.length > VALIDATION_RULES.BRAND.DESCRIPTION.MAX_LENGTH) {
      errors.push(`品牌描述不能超过 ${VALIDATION_RULES.BRAND.DESCRIPTION.MAX_LENGTH} 个字符`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: description ? description.trim() : null
  };
}

/**
 * 验证 URL
 * @param {string} url - URL 地址
 * @param {boolean} required - 是否必需
 * @returns {Object} 验证结果
 */
function validateUrl(url, required = false) {
  const errors = [];
  
  if (!url || typeof url !== 'string') {
    if (required) {
      errors.push('URL 不能为空');
    }
  } else {
    const trimmedUrl = url.trim();
    
    if (!VALIDATION_RULES.BRAND.URL.PATTERN.test(trimmedUrl)) {
      errors.push('URL 格式不正确，必须以 http:// 或 https:// 开头');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: url ? url.trim() : null
  };
}

/**
 * 验证用户名
 * @param {string} username - 用户名
 * @returns {Object} 验证结果
 */
function validateUsername(username) {
  const errors = [];
  
  if (!username || typeof username !== 'string') {
    errors.push('用户名不能为空');
  } else {
    const trimmedUsername = username.trim();
    
    if (trimmedUsername.length < VALIDATION_RULES.USER.USERNAME.MIN_LENGTH) {
      errors.push(`用户名至少需要 ${VALIDATION_RULES.USER.USERNAME.MIN_LENGTH} 个字符`);
    }
    
    if (trimmedUsername.length > VALIDATION_RULES.USER.USERNAME.MAX_LENGTH) {
      errors.push(`用户名不能超过 ${VALIDATION_RULES.USER.USERNAME.MAX_LENGTH} 个字符`);
    }
    
    if (!VALIDATION_RULES.USER.USERNAME.PATTERN.test(trimmedUsername)) {
      errors.push('用户名只能包含英文、数字、连字符和下划线');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: username ? username.trim() : null
  };
}

/**
 * 验证邮箱
 * @param {string} email - 邮箱地址
 * @param {boolean} required - 是否必需
 * @returns {Object} 验证结果
 */
function validateEmail(email, required = false) {
  const errors = [];
  
  if (!email || typeof email !== 'string') {
    if (required) {
      errors.push('邮箱地址不能为空');
    }
  } else {
    const trimmedEmail = email.trim().toLowerCase();
    
    if (!VALIDATION_RULES.USER.EMAIL.PATTERN.test(trimmedEmail)) {
      errors.push('邮箱地址格式不正确');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: email ? email.trim().toLowerCase() : null
  };
}

/**
 * 验证认证码
 * @param {string} authCode - 认证码
 * @returns {Object} 验证结果
 */
function validateAuthCode(authCode) {
  const errors = [];
  
  if (!authCode || typeof authCode !== 'string') {
    errors.push('认证码不能为空');
  } else {
    const trimmedAuthCode = authCode.trim();
    
    if (trimmedAuthCode.length < VALIDATION_RULES.USER.AUTH_CODE.MIN_LENGTH) {
      errors.push(`认证码至少需要 ${VALIDATION_RULES.USER.AUTH_CODE.MIN_LENGTH} 个字符`);
    }
    
    if (trimmedAuthCode.length > VALIDATION_RULES.USER.AUTH_CODE.MAX_LENGTH) {
      errors.push(`认证码不能超过 ${VALIDATION_RULES.USER.AUTH_CODE.MAX_LENGTH} 个字符`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: authCode ? authCode.trim() : null
  };
}

/**
 * 验证 MongoDB ObjectId
 * @param {string} id - ObjectId 字符串
 * @returns {Object} 验证结果
 */
function validateObjectId(id) {
  const errors = [];
  
  if (!id || typeof id !== 'string') {
    errors.push('ID 不能为空');
  } else if (!REGEX_PATTERNS.OBJECT_ID.test(id)) {
    errors.push('ID 格式不正确');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: id
  };
}

/**
 * 验证分页参数
 * @param {*} page - 页码
 * @param {*} limit - 每页数量
 * @returns {Object} 验证结果
 */
function validatePagination(page, limit) {
  const errors = [];
  const result = {
    page: VALIDATION_RULES.PAGINATION.DEFAULT_PAGE,
    limit: VALIDATION_RULES.PAGINATION.DEFAULT_LIMIT
  };
  
  // 验证页码
  if (page !== undefined) {
    const pageNum = parseInt(page, 10);
    if (isNaN(pageNum) || pageNum < 1) {
      errors.push('页码必须是大于 0 的整数');
    } else {
      result.page = pageNum;
    }
  }
  
  // 验证每页数量
  if (limit !== undefined) {
    const limitNum = parseInt(limit, 10);
    if (isNaN(limitNum) || limitNum < 1) {
      errors.push('每页数量必须是大于 0 的整数');
    } else if (limitNum > VALIDATION_RULES.PAGINATION.MAX_LIMIT) {
      errors.push(`每页数量不能超过 ${VALIDATION_RULES.PAGINATION.MAX_LIMIT}`);
    } else {
      result.limit = limitNum;
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: result
  };
}

/**
 * 验证排序参数
 * @param {string} sortBy - 排序字段
 * @param {string} sortOrder - 排序方向
 * @param {Array} allowedFields - 允许的排序字段
 * @returns {Object} 验证结果
 */
function validateSort(sortBy, sortOrder, allowedFields = []) {
  const errors = [];
  const result = {};
  
  if (sortBy) {
    if (allowedFields.length > 0 && !allowedFields.includes(sortBy)) {
      errors.push(`排序字段必须是以下之一: ${allowedFields.join(', ')}`);
    } else {
      result.sortBy = sortBy;
    }
  }
  
  if (sortOrder) {
    const order = sortOrder.toLowerCase();
    if (order !== 'asc' && order !== 'desc') {
      errors.push('排序方向必须是 asc 或 desc');
    } else {
      result.sortOrder = order === 'asc' ? 1 : -1;
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: result
  };
}

/**
 * 验证品牌数据
 * @param {Object} brandData - 品牌数据
 * @returns {Object} 验证结果
 */
function validateBrandData(brandData) {
  const errors = {};
  const validatedData = {};
  
  // 验证品牌名称
  const nameValidation = validateBrandName(brandData.name);
  if (!nameValidation.isValid) {
    errors.name = nameValidation.errors;
  } else {
    validatedData.name = nameValidation.value;
  }
  
  // 验证品牌描述
  const descriptionValidation = validateBrandDescription(brandData.description);
  if (!descriptionValidation.isValid) {
    errors.description = descriptionValidation.errors;
  } else if (descriptionValidation.value) {
    validatedData.description = descriptionValidation.value;
  }
  
  // 验证官网 URL
  const websiteValidation = validateUrl(brandData.website, false);
  if (!websiteValidation.isValid) {
    errors.website = websiteValidation.errors;
  } else if (websiteValidation.value) {
    validatedData.website = websiteValidation.value;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    data: validatedData
  };
}

/**
 * 验证用户数据
 * @param {Object} userData - 用户数据
 * @returns {Object} 验证结果
 */
function validateUserData(userData) {
  const errors = {};
  const validatedData = {};
  
  // 验证用户名
  if (userData.username !== undefined) {
    const usernameValidation = validateUsername(userData.username);
    if (!usernameValidation.isValid) {
      errors.username = usernameValidation.errors;
    } else {
      validatedData.username = usernameValidation.value;
    }
  }
  
  // 验证邮箱
  if (userData.email !== undefined) {
    const emailValidation = validateEmail(userData.email, false);
    if (!emailValidation.isValid) {
      errors.email = emailValidation.errors;
    } else if (emailValidation.value) {
      validatedData.email = emailValidation.value;
    }
  }
  
  // 验证认证码
  if (userData.authCode !== undefined) {
    const authCodeValidation = validateAuthCode(userData.authCode);
    if (!authCodeValidation.isValid) {
      errors.authCode = authCodeValidation.errors;
    } else {
      validatedData.authCode = authCodeValidation.value;
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    data: validatedData
  };
}

module.exports = {
  validateBrandName,
  validateBrandDescription,
  validateUrl,
  validateUsername,
  validateEmail,
  validateAuthCode,
  validateObjectId,
  validatePagination,
  validateSort,
  validateBrandData,
  validateUserData
};
