/**
 * 品牌控制器
 * 处理品牌相关的HTTP请求
 */

const brandService = require('../services/brandService');
const { success, created, paginated, notFound, serverError, asyncHandler } = require('../utils/response');
const { SUCCESS_MESSAGES } = require('../constants');

/**
 * 品牌控制器类
 */
class BrandController {
  /**
   * 获取品牌列表
   */
  getBrands = asyncHandler(async (req, res) => {
    const options = {
      page: req.query.page,
      limit: req.query.limit,
      status: req.query.status,
      active: req.query.active,
      search: req.query.search,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder,
      tags: req.query.tags
    };

    const result = await brandService.getBrands(options);
    
    return paginated(
      res,
      result.brands,
      result.pagination,
      SUCCESS_MESSAGES.RETRIEVED
    );
  });

  /**
   * 根据ID获取品牌
   */
  getBrandById = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const brand = await brandService.getBrandById(id);
    
    return success(res, brand, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 创建品牌
   */
  createBrand = asyncHandler(async (req, res) => {
    const brandData = req.body;
    const userId = req.user?.id; // 假设从认证中间件获取
    
    const brand = await brandService.createBrand(brandData, userId);
    
    return created(res, brand, SUCCESS_MESSAGES.CREATED);
  });

  /**
   * 更新品牌
   */
  updateBrand = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user?.id;
    
    const brand = await brandService.updateBrand(id, updateData, userId);
    
    return success(res, brand, SUCCESS_MESSAGES.UPDATED);
  });

  /**
   * 删除品牌
   */
  deleteBrand = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user?.id;
    
    const result = await brandService.deleteBrand(id, userId);
    
    return success(res, result, SUCCESS_MESSAGES.DELETED);
  });

  /**
   * 批量更新品牌状态
   */
  batchUpdateStatus = asyncHandler(async (req, res) => {
    const { ids, status } = req.body;
    const userId = req.user?.id;
    
    const result = await brandService.batchUpdateStatus(ids, status, userId);
    
    return success(res, result, SUCCESS_MESSAGES.UPDATED);
  });

  /**
   * 获取活跃品牌
   */
  getActiveBrands = asyncHandler(async (req, res) => {
    const options = {
      limit: req.query.limit,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const brands = await brandService.getActiveBrands(options);
    
    return success(res, brands, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 搜索品牌
   */
  searchBrands = asyncHandler(async (req, res) => {
    const { keyword } = req.query;
    const options = {
      status: req.query.status,
      limit: req.query.limit
    };

    const brands = await brandService.searchBrands(keyword, options);
    
    return success(res, brands, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 获取品牌统计信息
   */
  getBrandStats = asyncHandler(async (req, res) => {
    const stats = await brandService.getBrandStats();
    
    return success(res, stats, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 增加品牌浏览次数
   */
  incrementViews = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const result = await brandService.incrementViews(id);
    
    return success(res, result, '浏览次数已更新');
  });

  /**
   * 增加品牌点击次数
   */
  incrementClicks = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const result = await brandService.incrementClicks(id);
    
    return success(res, result, '点击次数已更新');
  });

  /**
   * 获取热门品牌
   */
  getPopularBrands = asyncHandler(async (req, res) => {
    const limit = req.query.limit || 10;
    const Brand = require('../models/Brand');
    
    const brands = await Brand.getPopular(parseInt(limit, 10));
    
    return success(res, brands, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 获取最新品牌
   */
  getLatestBrands = asyncHandler(async (req, res) => {
    const limit = req.query.limit || 10;
    const Brand = require('../models/Brand');
    
    const brands = await Brand.getLatest(parseInt(limit, 10));
    
    return success(res, brands, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 按标签获取品牌
   */
  getBrandsByTag = asyncHandler(async (req, res) => {
    const { tag } = req.params;
    const options = {
      limit: req.query.limit,
      skip: req.query.skip
    };
    
    const Brand = require('../models/Brand');
    const brands = await Brand.getByTag(tag, options);
    
    return success(res, brands, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 添加品牌标签
   */
  addBrandTag = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { tag } = req.body;
    
    const Brand = require('../models/Brand');
    const brand = await Brand.findById(id);
    
    if (!brand) {
      return notFound(res, '品牌未找到');
    }
    
    await brand.addTag(tag);
    
    return success(res, brand, '标签已添加');
  });

  /**
   * 移除品牌标签
   */
  removeBrandTag = asyncHandler(async (req, res) => {
    const { id, tag } = req.params;
    
    const Brand = require('../models/Brand');
    const brand = await Brand.findById(id);
    
    if (!brand) {
      return notFound(res, '品牌未找到');
    }
    
    await brand.removeTag(tag);
    
    return success(res, brand, '标签已移除');
  });

  /**
   * 激活品牌
   */
  activateBrand = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const Brand = require('../models/Brand');
    const brand = await Brand.findById(id);
    
    if (!brand) {
      return notFound(res, '品牌未找到');
    }
    
    await brand.activate();
    
    return success(res, brand, '品牌已激活');
  });

  /**
   * 停用品牌
   */
  deactivateBrand = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const Brand = require('../models/Brand');
    const brand = await Brand.findById(id);
    
    if (!brand) {
      return notFound(res, '品牌未找到');
    }
    
    await brand.deactivate();
    
    return success(res, brand, '品牌已停用');
  });

  /**
   * 更新品牌排序
   */
  updateBrandSort = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { sortOrder } = req.body;
    
    const Brand = require('../models/Brand');
    const brand = await Brand.findById(id);
    
    if (!brand) {
      return notFound(res, '品牌未找到');
    }
    
    brand.sortOrder = sortOrder;
    await brand.save();
    
    return success(res, brand, '排序已更新');
  });

  /**
   * 批量更新品牌排序
   */
  batchUpdateSort = asyncHandler(async (req, res) => {
    const { updates } = req.body; // [{ id, sortOrder }, ...]
    
    const Brand = require('../models/Brand');
    const promises = updates.map(update => 
      Brand.findByIdAndUpdate(update.id, { sortOrder: update.sortOrder })
    );
    
    await Promise.all(promises);
    
    return success(res, { updated: updates.length }, '批量排序已更新');
  });
}

module.exports = new BrandController();
