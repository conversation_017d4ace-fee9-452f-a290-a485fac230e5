<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>AI重器</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline';">
    <link href="../assets/styles/themes.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        p {
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }



        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        button {
            padding: 12px 24px;
            font-size: 1em;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        button:active {
            transform: translateY(0);
        }

        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            min-height: 20px;
        }

        .features {
            margin-top: 40px;
            text-align: left;
        }

        .features h3 {
            text-align: center;
            margin-bottom: 20px;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .features li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 <span data-i18n="app.title">AI重器</span></h1>
        <p data-i18n="app.welcome">欢迎使用这个跨平台的Electron应用！</p>

        <div class="buttons">
            <button onclick="openAdminPanel()">🛠️ <span data-i18n="main.buttons.admin_panel">管理面板</span></button>
            <button onclick="openSettings()">⚙️ <span data-i18n="main.buttons.settings">设置</span></button>
        </div>

        <div class="status" id="status" data-i18n="app.ready">准备就绪</div>

        <div class="features">
            <h3>✨ <span data-i18n="main.features.title">功能特性</span></h3>
            <ul>
                <li data-i18n="main.features.cross_platform">跨平台支持 (Windows, macOS, Linux)</li>
                <li data-i18n="main.features.auto_update">自动更新功能</li>
                <li data-i18n="main.features.github_actions">GitHub Actions 自动构建</li>
                <li data-i18n="main.features.modern_ui">现代化的用户界面</li>
                <li data-i18n="main.features.dev_friendly">开发者友好的配置</li>
            </ul>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
