/**
 * 应用常量定义
 * 统一管理应用中使用的常量
 */

// 应用信息
const APP_INFO = {
  NAME: 'AI重器',
  VERSION: '1.0.0',
  DESCRIPTION: 'AI重器 - 跨平台的AI工具集合',
  AUTHOR: 'laixiao',
  HOMEPAGE: 'https://github.com/laixiao/AiTools'
};

// 支持的语言
const LANGUAGES = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US'
};

// 语言显示名称
const LANGUAGE_NAMES = {
  [LANGUAGES.ZH_CN]: '中文 (简体)',
  [LANGUAGES.EN_US]: 'English'
};

// 主题类型
const THEMES = {
  AUTO: 'auto',
  LIGHT: 'light',
  DARK: 'dark'
};

// 主题显示名称
const THEME_NAMES = {
  [THEMES.AUTO]: '跟随系统',
  [THEMES.LIGHT]: '浅色',
  [THEMES.DARK]: '深色'
};

// 用户类型
const USER_TYPES = {
  USER: 'user',
  ADMIN: 'admin'
};

// 用户类型显示名称
const USER_TYPE_NAMES = {
  [USER_TYPES.USER]: '普通用户',
  [USER_TYPES.ADMIN]: '管理员'
};

// 更新通道
const UPDATE_CHANNELS = {
  GITHUB: 'github',
  GITEE: 'gitee',
  CUSTOM: 'custom'
};

// 更新通道显示名称
const UPDATE_CHANNEL_NAMES = {
  [UPDATE_CHANNELS.GITHUB]: 'GitHub (默认)',
  [UPDATE_CHANNELS.GITEE]: 'Gitee (中国镜像)',
  [UPDATE_CHANNELS.CUSTOM]: '自定义'
};

// 更新通道 URL
const UPDATE_CHANNEL_URLS = {
  [UPDATE_CHANNELS.GITHUB]: 'https://github.com/laixiao/AiTools/releases',
  [UPDATE_CHANNELS.GITEE]: 'https://gitee.com/laixiao/AiTools/releases'
};

// 默认设置
const DEFAULT_SETTINGS = {
  language: LANGUAGES.ZH_CN,
  theme: THEMES.AUTO,
  autoStart: false,
  minimizeToTray: false,
  autoUpdate: true,
  updateChannel: UPDATE_CHANNELS.GITHUB,
  customUpdateUrl: ''
};

// IPC 事件名称
const IPC_EVENTS = {
  // 设置相关
  GET_SETTING: 'get-setting',
  SET_SETTING: 'set-setting',
  GET_ALL_SETTINGS: 'get-all-settings',
  SAVE_ALL_SETTINGS: 'save-all-settings',
  
  // 语言相关
  GET_I18N_TEXTS: 'get-i18n-texts',
  LANGUAGE_CHANGED: 'language-changed',
  
  // 主题相关
  APPLY_THEME: 'apply-theme',
  THEME_CHANGED: 'theme-changed',
  
  // 用户相关
  GET_CURRENT_USER: 'get-current-user',
  LOGOUT: 'logout',
  
  // 窗口相关
  SWITCH_TO_MAIN: 'switch-to-main',
  SWITCH_TO_LOGIN: 'switch-to-login',
  SWITCH_TO_SETTINGS: 'switch-to-settings',
  SWITCH_TO_ADMIN: 'switch-to-admin',
  
  // 更新相关
  CHECK_FOR_UPDATES: 'check-for-updates',
  CHECK_FOR_UPDATES_WITH_CHANNEL: 'check-for-updates-with-channel',
  
  // 应用信息
  GET_APP_VERSION: 'get-app-version'
};

// 页面路径
const PAGES = {
  MAIN: 'index.html',
  LOGIN: 'login.html',
  SETTINGS: 'settings.html',
  ADMIN: 'admin.html'
};

// 状态码
const STATUS_CODES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// 本地存储键名
const STORAGE_KEYS = {
  SETTINGS: 'app-settings',
  USER_DATA: 'user-data',
  THEME: 'app-theme',
  LANGUAGE: 'app-language'
};

// 文件路径
const FILE_PATHS = {
  SETTINGS: 'settings.json',
  USER_DATA: 'user-data.json',
  LOGS: 'logs'
};

// 网络相关
const NETWORK = {
  TIMEOUT: 10000,
  RETRY_TIMES: 3,
  MAX_CONCURRENT_REQUESTS: 5
};

// 验证规则
const VALIDATION = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[a-zA-Z0-9_-]+$/
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  AUTH_CODE: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 50
  },
  BRAND_NAME: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 50
  },
  DESCRIPTION: {
    MAX_LENGTH: 500
  }
};

// 分页配置
const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100]
};

// 错误消息
const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败',
  TIMEOUT_ERROR: '请求超时',
  VALIDATION_ERROR: '数据验证失败',
  PERMISSION_ERROR: '权限不足',
  NOT_FOUND_ERROR: '资源未找到',
  SERVER_ERROR: '服务器内部错误',
  UNKNOWN_ERROR: '未知错误'
};

// 成功消息
const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  UPDATE_SUCCESS: '更新成功',
  CREATE_SUCCESS: '创建成功',
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出成功'
};

// 动画配置
const ANIMATIONS = {
  DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500
  },
  EASING: {
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out'
  }
};

// 导出所有常量
module.exports = {
  APP_INFO,
  LANGUAGES,
  LANGUAGE_NAMES,
  THEMES,
  THEME_NAMES,
  USER_TYPES,
  USER_TYPE_NAMES,
  UPDATE_CHANNELS,
  UPDATE_CHANNEL_NAMES,
  UPDATE_CHANNEL_URLS,
  DEFAULT_SETTINGS,
  IPC_EVENTS,
  PAGES,
  STATUS_CODES,
  STORAGE_KEYS,
  FILE_PATHS,
  NETWORK,
  VALIDATION,
  PAGINATION,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  ANIMATIONS
};
