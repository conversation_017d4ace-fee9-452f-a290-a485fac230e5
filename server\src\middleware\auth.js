/**
 * 认证中间件
 * 处理用户认证和权限验证
 */

const User = require('../models/User');
const { unauthorized, forbidden } = require('../utils/response');
const { USER_TYPES } = require('../constants');

/**
 * 简单的认证中间件（基于认证码）
 * 在实际项目中，建议使用 JWT 或其他更安全的认证方式
 */
const requireAuth = async (req, res, next) => {
  try {
    // 只从Authorization header获取认证码
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return unauthorized(res, '缺少认证信息');
    }

    const authCode = authHeader.replace('Bearer ', '');
    if (!authCode) {
      return unauthorized(res, '缺少认证信息');
    }

    // 查找用户
    const user = await User.findByAuthCode(authCode);
    if (!user) {
      return unauthorized(res, '无效的认证码');
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: user._id.toString(),
      username: user.username,
      userType: user.userType,
      isAdmin: user.userType === USER_TYPES.ADMIN,
      isActive: user.isActive,
      displayName: user.displayName,
      email: user.email
    };

    console.log('=== 认证中间件调试 ===');
    console.log('找到用户:', user.username);
    console.log('设置 req.user.id:', req.user.id);

    next();
  } catch (error) {
    console.error('认证中间件错误:', error);
    return unauthorized(res, '认证失败');
  }
};

/**
 * 可选认证中间件
 * 如果提供了认证信息则验证，否则继续执行
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const authCode = authHeader.replace('Bearer ', '');
      if (authCode) {
        const user = await User.findByAuthCode(authCode);
        if (user) {
          req.user = {
            id: user._id.toString(),
            username: user.username,
            userType: user.userType,
            isAdmin: user.userType === USER_TYPES.ADMIN,
            isActive: user.isActive,
            displayName: user.displayName,
            email: user.email
          };
        }
      }
    }

    next();
  } catch (error) {
    console.error('可选认证中间件错误:', error);
    // 可选认证失败时不阻止请求继续
    next();
  }
};

/**
 * 管理员权限验证中间件
 */
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return unauthorized(res, '需要登录');
  }

  if (!req.user.isAdmin) {
    return forbidden(res, '需要管理员权限');
  }

  next();
};

/**
 * 用户类型验证中间件
 * @param {string|Array} allowedTypes - 允许的用户类型
 */
const requireUserType = (allowedTypes) => {
  return (req, res, next) => {
    if (!req.user) {
      return unauthorized(res, '需要登录');
    }

    const types = Array.isArray(allowedTypes) ? allowedTypes : [allowedTypes];
    
    if (!types.includes(req.user.userType)) {
      return forbidden(res, '权限不足');
    }

    next();
  };
};

/**
 * 活跃用户验证中间件
 */
const requireActiveUser = (req, res, next) => {
  if (!req.user) {
    return unauthorized(res, '需要登录');
  }

  if (!req.user.isActive) {
    return forbidden(res, '账户已被停用');
  }

  next();
};

/**
 * 资源所有者验证中间件
 * 验证用户是否为资源的所有者或管理员
 * @param {string} paramName - 参数名称（默认为 'id'）
 * @param {string} userIdField - 用户ID字段名称（默认为 'id'）
 */
const requireOwnerOrAdmin = (paramName = 'id', userIdField = 'id') => {
  return (req, res, next) => {
    if (!req.user) {
      return unauthorized(res, '需要登录');
    }

    const resourceUserId = req.params[paramName];
    const currentUserId = req.user[userIdField];

    // 管理员可以访问任何资源
    if (req.user.isAdmin) {
      return next();
    }

    // 用户只能访问自己的资源
    if (resourceUserId !== currentUserId) {
      return forbidden(res, '只能访问自己的资源');
    }

    next();
  };
};

/**
 * API 密钥验证中间件
 * 用于服务间调用或特殊API访问
 */
const requireApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  const validApiKey = process.env.API_KEY;

  if (!validApiKey) {
    console.warn('API_KEY 环境变量未设置');
    return forbidden(res, 'API密钥验证未配置');
  }

  if (!apiKey || apiKey !== validApiKey) {
    return unauthorized(res, '无效的API密钥');
  }

  next();
};

/**
 * 速率限制中间件
 * 基于用户的简单速率限制
 */
const createRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();

  return (req, res, next) => {
    const key = req.user?.id || req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;

    // 清理过期记录
    if (requests.has(key)) {
      const userRequests = requests.get(key).filter(time => time > windowStart);
      requests.set(key, userRequests);
    }

    // 检查请求次数
    const userRequests = requests.get(key) || [];
    if (userRequests.length >= maxRequests) {
      return res.status(429).json({
        status: 'error',
        message: '请求过于频繁，请稍后再试',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    // 记录当前请求
    userRequests.push(now);
    requests.set(key, userRequests);

    next();
  };
};

/**
 * 权限检查辅助函数
 */
const hasPermission = (user, permission) => {
  if (!user) return false;
  
  // 管理员拥有所有权限
  if (user.isAdmin) return true;
  
  // 这里可以扩展更复杂的权限系统
  // 例如基于角色的权限控制 (RBAC)
  
  return false;
};

/**
 * 权限验证中间件
 * @param {string} permission - 所需权限
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return unauthorized(res, '需要登录');
    }

    if (!hasPermission(req.user, permission)) {
      return forbidden(res, '权限不足');
    }

    next();
  };
};

/**
 * 开发环境认证绕过中间件
 * 仅在开发环境使用，生产环境禁用
 */
const devBypassAuth = (req, res, next) => {
  if (process.env.NODE_ENV === 'development' && process.env.DEV_BYPASS_AUTH === 'true') {
    // 在开发环境中创建一个模拟管理员用户
    req.user = {
      id: 'dev-admin',
      username: 'dev-admin',
      userType: USER_TYPES.ADMIN,
      isAdmin: true,
      isActive: true,
      displayName: '开发管理员',
      email: '<EMAIL>'
    };
  }
  
  next();
};

module.exports = {
  requireAuth,
  optionalAuth,
  requireAdmin,
  requireUserType,
  requireActiveUser,
  requireOwnerOrAdmin,
  requireApiKey,
  createRateLimit,
  requirePermission,
  devBypassAuth,
  hasPermission
};
