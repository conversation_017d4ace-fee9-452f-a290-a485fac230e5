# 部署指南

## 📦 项目结构

AI重器采用前后端分离架构：

- **client/** - Electron桌面应用
- **server/** - Node.js后端服务

## 🚀 本地部署

### 1. 环境要求

- Node.js 18+
- MongoDB数据库
- Git

### 2. 克隆项目

```bash
git clone https://github.com/laixiao/AiTools.git
cd AiTools
```

### 3. 安装依赖

```bash
# 安装客户端依赖
cd client
npm install

# 安装服务端依赖
cd ../server
npm install
```

### 4. 配置环境

编辑 `server/.env` 文件：

```env
PORT=3000
MONGODB_URI=*************************************************************
NODE_ENV=development
```

### 5. 启动应用

```bash
# 方式1: 使用启动脚本
start.bat

# 方式2: 分别启动
start-server.bat  # 启动后端
start-client.bat  # 启动前端

# 方式3: 手动启动
cd server && npm start    # 终端1
cd client && npm start    # 终端2
```

## 🏗️ 生产部署

### 后端服务部署

后端服务需要手动部署到您的服务器。基本步骤：

1. 将 `server/` 目录部署到服务器
2. 安装依赖：`npm install --production`
3. 配置环境变量（.env文件）
4. 启动服务：`npm start` 或使用PM2等进程管理器

### 客户端应用打包

1. **构建安装包**
   ```bash
   cd client
   npm run build        # 构建所有平台
   npm run build:win    # 仅Windows
   npm run build:mac    # 仅macOS
   npm run build:linux  # 仅Linux
   ```

2. **分发安装包**
   - Windows: `client/dist/*.exe`
   - macOS: `client/dist/*.dmg`
   - Linux: `client/dist/*.AppImage`

## 🔄 自动化部署

### GitHub Actions（仅客户端）

项目配置了客户端自动化构建：

1. **推送代码触发构建**
   ```bash
   git add .
   git commit -m "feat: 新功能"
   git push origin main
   ```

2. **创建发布版本**
   ```bash
   # 更新版本号
   cd client
   npm version patch  # 或 minor, major

   # 创建tag并推送
   git tag v1.0.5
   git push origin v1.0.5
   ```

3. **自动发布**
   - GitHub Actions自动构建客户端
   - 生成Electron安装包
   - 发布到GitHub Releases

**注意**: 后端服务需要手动部署

## 🔧 故障排除

### 常见问题

1. **Electron启动失败**
   ```bash
   # 重新安装依赖
   cd client
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **后端连接失败**
   ```bash
   # 检查MongoDB连接
   # 检查端口占用
   netstat -an | findstr :3000
   ```

3. **构建失败**
   ```bash
   # 清理构建缓存
   cd client
   rm -rf dist
   npm run build
   ```

## 📊 监控和维护

### 日志管理

```bash
# 查看PM2日志
pm2 logs aitools-server

# 查看实时日志
pm2 logs aitools-server --lines 100
```

### 性能监控

```bash
# PM2监控
pm2 monit

# 重启服务
pm2 restart aitools-server
```

## 🔐 安全配置

1. **环境变量**
   - 不要在代码中硬编码敏感信息
   - 使用.env文件管理配置

2. **数据库安全**
   - 使用强密码
   - 限制数据库访问IP
   - 定期备份数据

3. **应用安全**
   - 定期更新依赖
   - 使用HTTPS
   - 实施访问控制
