/**
 * 文件服务层
 * 处理文件相关的业务逻辑
 */

const { File, FILE_STATUS, FILE_TYPES } = require('../models/File');
const { deleteFile, getFileInfo } = require('../middleware/upload');
const path = require('path');
const fs = require('fs');

/**
 * 文件服务类
 */
class FileService {
  /**
   * 创建文件记录
   */
  async createFile(fileData, uploadedBy) {
    try {
      const file = new File({
        ...fileData,
        uploadedBy
      });
      
      await file.save();
      await file.populate('uploadedBy', 'username displayName');
      
      return file;
    } catch (error) {
      // 如果保存失败，删除已上传的文件
      if (fileData.path && fs.existsSync(fileData.path)) {
        await deleteFile(fileData.path);
      }
      throw error;
    }
  }

  /**
   * 获取文件列表
   */
  async getFiles(options = {}) {
    const {
      page = 1,
      limit = 20,
      fileType,
      status = FILE_STATUS.ACTIVE,
      search,
      uploadedBy,
      tags,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      userId,
      userType
    } = options;

    // 构建查询条件
    const query = { status };

    if (fileType) {
      query.fileType = fileType;
    }

    if (uploadedBy) {
      query.uploadedBy = uploadedBy;
    }

    if (tags) {
      query.tags = { $in: tags.split(',') };
    }

    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { originalName: searchRegex },
        { description: searchRegex },
        { tags: searchRegex }
      ];
    }

    // 权限控制：非管理员只能看到自己上传的文件或公开文件
    if (userType !== 'admin') {
      query.$or = [
        { uploadedBy: userId },
        { isPublic: true }
      ];
    }

    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const files = await File.find(query)
      .populate('uploadedBy', 'username displayName')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await File.countDocuments(query);

    const pagination = {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    };

    return { files, pagination };
  }

  /**
   * 根据ID获取文件
   */
  async getFileById(id, userId, userType) {
    const file = await File.findById(id).populate('uploadedBy', 'username displayName');

    if (!file) {
      throw new Error('文件不存在');
    }

    // 权限检查
    if (file.status === FILE_STATUS.DELETED) {
      throw new Error('文件已被删除');
    }

    if (!file.isPublic && file.uploadedBy._id.toString() !== userId.toString() && userType !== 'admin') {
      throw new Error('没有权限访问此文件');
    }

    return file;
  }

  /**
   * 更新文件信息
   */
  async updateFile(id, updateData, userId, userType) {
    const file = await File.findById(id);

    if (!file) {
      throw new Error('文件不存在');
    }

    // 权限检查
    if (file.uploadedBy.toString() !== userId.toString() && userType !== 'admin') {
      throw new Error('没有权限修改此文件');
    }

    // 更新字段
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        if (key === 'tags' && typeof updateData[key] === 'string') {
          file[key] = updateData[key].split(',').map(tag => tag.trim());
        } else {
          file[key] = updateData[key];
        }
      }
    });

    await file.save();
    await file.populate('uploadedBy', 'username displayName');

    return file;
  }

  /**
   * 删除文件
   */
  async deleteFile(id, userId, userType, hardDelete = false) {
    const file = await File.findById(id);

    if (!file) {
      throw new Error('文件不存在');
    }

    // 权限检查
    if (file.uploadedBy.toString() !== userId.toString() && userType !== 'admin') {
      throw new Error('没有权限删除此文件');
    }

    if (hardDelete) {
      // 物理删除文件
      if (fs.existsSync(file.path)) {
        await deleteFile(file.path);
      }
      await File.findByIdAndDelete(id);
    } else {
      // 软删除
      await file.softDelete();
    }

    return file;
  }

  /**
   * 恢复文件
   */
  async restoreFile(id, userType) {
    if (userType !== 'admin') {
      throw new Error('没有权限恢复文件');
    }

    const file = await File.findById(id);

    if (!file) {
      throw new Error('文件不存在');
    }

    await file.restore();
    return file;
  }

  /**
   * 批量删除文件
   */
  async batchDeleteFiles(fileIds, userId, userType) {
    if (!Array.isArray(fileIds) || fileIds.length === 0) {
      throw new Error('请提供要删除的文件ID列表');
    }

    // 查找文件并检查权限
    const files = await File.find({ _id: { $in: fileIds } });
    
    for (const file of files) {
      if (file.uploadedBy.toString() !== userId.toString() && userType !== 'admin') {
        throw new Error(`没有权限删除文件: ${file.originalName}`);
      }
    }

    // 批量软删除
    await File.updateMany(
      { _id: { $in: fileIds } },
      { status: FILE_STATUS.DELETED }
    );

    return { deletedCount: files.length };
  }

  /**
   * 获取文件统计信息
   */
  async getFileStats() {
    const stats = await File.getStats();
    
    const totalFiles = await File.countDocuments({ status: FILE_STATUS.ACTIVE });
    const totalSize = await File.aggregate([
      { $match: { status: FILE_STATUS.ACTIVE } },
      { $group: { _id: null, total: { $sum: '$size' } } }
    ]);

    return {
      totalFiles,
      totalSize: totalSize[0]?.total || 0,
      byType: stats
    };
  }

  /**
   * 根据关联对象获取文件
   */
  async getFilesByRelation(model, objectId, options = {}) {
    const { limit, skip, status = FILE_STATUS.ACTIVE } = options;
    
    let query = File.find({ 
      'relatedTo.model': model,
      'relatedTo.objectId': objectId,
      status 
    });
    
    if (skip) query = query.skip(skip);
    if (limit) query = query.limit(limit);
    
    return query.sort({ createdAt: -1 }).populate('uploadedBy', 'username displayName');
  }

  /**
   * 清理孤儿文件（没有数据库记录的文件）
   */
  async cleanupOrphanFiles(uploadDir) {
    try {
      const files = fs.readdirSync(uploadDir);
      const orphanFiles = [];

      for (const filename of files) {
        const filePath = path.join(uploadDir, filename);
        const fileRecord = await File.findOne({ filename });

        if (!fileRecord) {
          orphanFiles.push(filePath);
        }
      }

      // 删除孤儿文件
      for (const filePath of orphanFiles) {
        await deleteFile(filePath);
      }

      return { cleanedCount: orphanFiles.length };
    } catch (error) {
      console.error('清理孤儿文件失败:', error);
      throw error;
    }
  }

  /**
   * 验证文件完整性
   */
  async validateFileIntegrity() {
    const files = await File.find({ status: FILE_STATUS.ACTIVE });
    const missingFiles = [];

    for (const file of files) {
      if (!fs.existsSync(file.path)) {
        missingFiles.push(file);
        // 标记为已删除
        await file.softDelete();
      }
    }

    return { missingCount: missingFiles.length, missingFiles };
  }

  /**
   * 获取用户的存储使用情况
   */
  async getUserStorageUsage(userId) {
    const result = await File.aggregate([
      {
        $match: {
          uploadedBy: userId,
          status: FILE_STATUS.ACTIVE
        }
      },
      {
        $group: {
          _id: '$fileType',
          count: { $sum: 1 },
          totalSize: { $sum: '$size' }
        }
      }
    ]);

    const totalUsage = await File.aggregate([
      {
        $match: {
          uploadedBy: userId,
          status: FILE_STATUS.ACTIVE
        }
      },
      {
        $group: {
          _id: null,
          totalFiles: { $sum: 1 },
          totalSize: { $sum: '$size' }
        }
      }
    ]);

    return {
      byType: result,
      total: totalUsage[0] || { totalFiles: 0, totalSize: 0 }
    };
  }
}

module.exports = new FileService();
