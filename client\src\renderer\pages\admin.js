const { ipc<PERSON>ender<PERSON> } = require('electron');

// 全局变量
let brands = [];
let users = [];
let currentBrandId = null;
let currentUserId = null;
let deleteTarget = { type: '', id: '', name: '' };

// 文件上传相关
let brandIconUploader = null;
let uploadedIconFile = null;

// 国际化文本
let i18nTexts = {};

// API 基础URL - 从环境配置获取
let API_BASE = '';

// 初始化API基础URL
async function initApiBase() {
    try {
        const config = await window.ipcRenderer.invoke('get-api-config');
        if (config && config.BASE_URL) {
            API_BASE = `${config.BASE_URL}/api`;

            // 同时更新CSP策略
            await updateCSP(config.BASE_URL);
            console.log('API配置初始化成功:', API_BASE);
        } else {
            throw new Error('API配置无效或为空');
        }
    } catch (error) {
        console.error('获取API配置失败:', error);
        // 显示错误提示
        showApiConfigError(error.message);
        // 不设置回退值，让用户知道配置有问题
        throw error;
    }
}

// 显示API配置错误
function showApiConfigError(errorMessage) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger';
    errorDiv.style.cssText = 'position: fixed; top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; max-width: 500px;';
    errorDiv.innerHTML = `
        <h5><i class="bi bi-exclamation-triangle"></i> API配置错误</h5>
        <p>无法获取API配置: ${errorMessage}</p>
        <p>请检查：</p>
        <ul>
            <li>.env文件是否存在且配置正确</li>
            <li>API服务器是否正在运行</li>
            <li>网络连接是否正常</li>
        </ul>
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()">关闭</button>
    `;
    document.body.appendChild(errorDiv);

    // 5秒后自动移除
    setTimeout(() => {
        if (errorDiv.parentElement) {
            errorDiv.remove();
        }
    }, 10000);
}

// 动态更新CSP策略
async function updateCSP(baseUrl) {
    try {
        const cspMeta = document.getElementById('csp-meta');
        if (cspMeta && baseUrl) {
            const currentContent = cspMeta.getAttribute('content');
            // 如果CSP中还没有包含当前的API基础URL，则添加
            if (!currentContent.includes(baseUrl)) {
                const newContent = currentContent.replace('http: https:', `http: https: ${baseUrl}`);
                cspMeta.setAttribute('content', newContent);
            }
        }
    } catch (error) {
        console.error('更新CSP失败:', error);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // 初始化API基础URL
        await initApiBase();

        // 加载国际化文本
        await loadI18nTexts();

        // 加载并应用主题
        await loadAndApplyTheme();

        loadCurrentUser();
        loadBrands();
        loadUsers();
        loadFiles();
        setupEventListeners();
    } catch (error) {
        console.error('页面初始化失败:', error);
        // 仍然加载基本的UI元素
        await loadI18nTexts();
        await loadAndApplyTheme();
        setupEventListeners();

        // 禁用需要API的功能
        disableApiDependentFeatures();
    }
});

// 禁用依赖API的功能
function disableApiDependentFeatures() {
    // 禁用所有需要API的按钮
    const apiButtons = document.querySelectorAll('[onclick*="Brand"], [onclick*="User"], [onclick*="File"]');
    apiButtons.forEach(button => {
        button.disabled = true;
        button.title = 'API配置错误，功能不可用';
    });

    // 在主要内容区域显示错误信息
    const mainContent = document.querySelector('.container-fluid');
    if (mainContent) {
        const errorBanner = document.createElement('div');
        errorBanner.className = 'alert alert-warning mt-3';
        errorBanner.innerHTML = `
            <h5><i class="bi bi-exclamation-triangle"></i> 功能受限</h5>
            <p>由于API配置问题，部分功能暂时不可用。请检查配置后刷新页面。</p>
        `;
        mainContent.insertBefore(errorBanner, mainContent.firstChild);
    }
}

// 加载当前用户信息
async function loadCurrentUser() {
    try {
        console.log('管理面板：开始获取当前用户信息...');
        const user = await ipcRenderer.invoke('get-current-user');
        console.log('管理面板：获取到的用户信息:', user);

        if (user) {
            document.getElementById('userInfo').innerHTML = `
                <i class="bi bi-person-circle me-1"></i>${user.displayName || user.username} (${user.userTypeDisplay})
            `;
        } else {
            console.warn('管理面板：未获取到用户信息');
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 品牌图标URL输入框变化时更新预览
    document.getElementById('brandIcon').addEventListener('input', updateBrandIconPreview);

    // 表单提交
    document.getElementById('brandForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveBrand();
    });

    document.getElementById('userForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveUser();
    });

    // 监听品牌模态框显示事件，初始化文件上传组件
    const brandModal = document.getElementById('brandModal');
    brandModal.addEventListener('shown.bs.modal', function() {
        if (!brandIconUploader) {
            initBrandIconUploader();
        }
    });
}

// 初始化品牌图标上传组件
function initBrandIconUploader() {
    const uploadContainer = document.getElementById('brandIconUpload');
    if (!uploadContainer) {
        console.error('找不到文件上传容器: brandIconUpload');
        return;
    }

    // 检查FileUpload类是否可用
    if (typeof FileUpload === 'undefined') {
        console.error('FileUpload类未定义，请确保已正确加载FileUpload.js');
        return;
    }

    console.log('正在初始化品牌图标上传组件...');

    try {
        // 使用动态API基础URL
        const uploadUrl = `${API_BASE.replace('/api', '')}/api/files/upload-image`;
        console.log('初始化文件上传组件，使用URL:', uploadUrl);

        brandIconUploader = new FileUpload({
            container: uploadContainer,
            accept: 'image/*',
            maxSize: 5 * 1024 * 1024, // 5MB
            multiple: false,
            autoUpload: false, // 手动上传
            uploadUrl: uploadUrl,
            onSuccess: (file) => {
                console.log('图标上传成功:', file);
                uploadedIconFile = file;

                // 更新图标URL输入框
                const iconInput = document.getElementById('brandIcon');
                const baseUrl = API_BASE.replace('/api', '');
                iconInput.value = `${baseUrl}${file.url}`;

                // 更新预览
                updateBrandIconPreview();

                showAlert('图标上传成功', 'success');
            },
            onError: (error) => {
                console.error('图标上传失败:', error);
                showAlert('图标上传失败: ' + error.message, 'danger');
            },
            relatedModel: 'Brand',
            category: 'brand-icon',
            isPublic: true
        });

        console.log('品牌图标上传组件初始化成功');
    } catch (error) {
        console.error('初始化文件上传组件失败:', error);
        showAlert('文件上传组件初始化失败', 'danger');
    }
}

// 返回主界面
async function goBack() {
    try {
        await ipcRenderer.invoke('switch-to-main');
    } catch (error) {
        console.error('返回主界面失败:', error);
    }
}

// 加载国际化文本
async function loadI18nTexts() {
    try {
        const language = await ipcRenderer.invoke('get-setting', 'language') || 'zh-CN';
        i18nTexts = await ipcRenderer.invoke('get-i18n-texts', language);
        updateI18nTexts();
    } catch (error) {
        console.error('加载国际化文本失败:', error);
    }
}

// 更新界面文本
function updateI18nTexts() {
    // 更新文本内容
    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        const text = getNestedValue(i18nTexts, key);
        if (text) {
            element.textContent = text;
        }
    });

    // 更新placeholder属性
    document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
        const key = element.getAttribute('data-i18n-placeholder');
        const text = getNestedValue(i18nTexts, key);
        if (text) {
            element.placeholder = text;
        }
    });

    // 更新title属性
    document.querySelectorAll('[data-i18n-title]').forEach(element => {
        const key = element.getAttribute('data-i18n-title');
        const text = getNestedValue(i18nTexts, key);
        if (text) {
            element.title = text;
        }
    });

    // 重新渲染文件列表以应用新的语言
    if (typeof renderFileList === 'function') {
        renderFileList();
    }
}

// 获取嵌套对象的值
function getNestedValue(obj, key) {
    return key.split('.').reduce((current, keyPart) => {
        return current && current[keyPart] !== undefined ? current[keyPart] : undefined;
    }, obj);
}

// 监听语言更改事件
ipcRenderer.on('language-changed', async (event, newLanguage) => {
    console.log('管理页面收到语言更改通知:', newLanguage);
    await loadI18nTexts();
});

// 监听主题更改事件
ipcRenderer.on('theme-changed', (event, theme) => {
    console.log('管理页面收到主题更改通知:', theme);
    applyTheme(theme);
});

// 加载并应用主题
async function loadAndApplyTheme() {
    try {
        const theme = await ipcRenderer.invoke('get-setting', 'theme') || 'auto';
        applyTheme(theme);
    } catch (error) {
        console.error('加载主题失败:', error);
        applyTheme('auto');
    }
}

// 应用主题
function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    console.log('管理页面主题已应用:', theme);
}

// 加载品牌列表
async function loadBrands() {
    try {
        showLoading(true);
        console.log('开始加载品牌列表...');

        const response = await fetch(`${API_BASE}/brands`);
        const result = await response.json();

        console.log('品牌API响应:', result);

        if (result.status === 'success') {
            brands = result.data;
            console.log('品牌数据:', brands);
            renderBrandList();
            updateBrandCount();
        } else {
            showAlert('加载品牌列表失败: ' + (result.message || result.error), 'danger');
        }
    } catch (error) {
        console.error('加载品牌列表错误:', error);
        showAlert('网络错误: ' + error.message, 'danger');
    } finally {
        showLoading(false);
    }
}

// 加载用户列表
async function loadUsers() {
    try {
        console.log('开始加载用户列表...');

        // 获取当前用户信息（包含认证信息）
        const currentUser = await ipcRenderer.invoke('get-current-user');
        console.log('当前用户:', currentUser);

        if (!currentUser || !currentUser.authCode) {
            showAlert('无法获取用户认证信息', 'danger');
            return;
        }

        console.log('使用的认证码:', currentUser.authCode);

        const response = await fetch(`${API_BASE}/users`, {
            headers: {
                'Authorization': `Bearer ${currentUser.authCode}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        console.log('用户API响应:', result);

        if (result.status === 'success') {
            users = result.data;
            console.log('用户数据:', users);
            renderUserList();
            updateUserCount();
        } else {
            showAlert('加载用户列表失败: ' + (result.message || result.error), 'danger');
        }
    } catch (error) {
        console.error('加载用户列表错误:', error);
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 渲染品牌列表
function renderBrandList() {
    const container = document.getElementById('brandList');
    
    if (brands.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-inbox display-4 text-muted"></i>
                <p class="text-muted mt-2">暂无品牌数据</p>
            </div>
        `;
        return;
    }
    
    const table = `
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>图标</th>
                    <th>名称</th>
                    <th>描述</th>
                    <th>状态</th>
                    <th>排序</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${brands.map(brand => `
                    <tr class="sortable" data-id="${brand._id}">
                        <td>
                            <img src="${brand.icon}" alt="${brand.name}" class="brand-icon" 
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNSAzNUMzMC41MjI4IDM1IDM1IDMwLjUyMjggMzUgMjVDMzUgMTkuNDc3MiAzMC41MjI4IDE1IDI1IDE1QzE5LjQ3NzIgMTUgMTUgMTkuNDc3MiAxNSAyNUMxNSAzMC41MjI4IDE5LjQ3NzIgMzUgMjUgMzVaIiBmaWxsPSIjOUNBM0FGIi8+CjwvcmVnPgo='">
                        </td>
                        <td><strong>${brand.name}</strong></td>
                        <td>${brand.description || '-'}</td>
                        <td>
                            <span class="badge ${brand.isActive ? 'bg-success' : 'bg-secondary'}">
                                ${brand.isActive ? '启用' : '禁用'}
                            </span>
                        </td>
                        <td>${brand.sortOrder}</td>
                        <td>${new Date(brand.createdAt).toLocaleDateString('zh-CN')}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editBrand('${brand._id}')" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteBrand('${brand._id}', '${brand.name}')" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    
    container.innerHTML = table;
}

// 渲染用户列表
function renderUserList() {
    const container = document.getElementById('userList');
    
    if (users.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-inbox display-4 text-muted"></i>
                <p class="text-muted mt-2">暂无用户数据</p>
            </div>
        `;
        return;
    }
    
    const table = `
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>用户名</th>
                    <th>邮箱</th>
                    <th>显示名称</th>
                    <th>类型</th>
                    <th>状态</th>
                    <th>授权码</th>
                    <th>登录次数</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${users.map(user => `
                    <tr data-id="${user._id}">
                        <td><strong>${user.username}</strong></td>
                        <td>${user.email}</td>
                        <td>${user.displayName || '-'}</td>
                        <td>
                            <span class="badge user-type-badge ${user.userType === 'admin' ? 'bg-danger' : 'bg-info'}">
                                ${user.userTypeDisplay}
                            </span>
                        </td>
                        <td>
                            <span class="badge ${user.isActive ? 'bg-success' : 'bg-secondary'}">
                                ${user.isActive ? '活跃' : '禁用'}
                            </span>
                        </td>
                        <td>
                            <code class="small">${user.authCode || '***'}</code>
                        </td>
                        <td>${user.loginCount || 0}</td>
                        <td>${user.formattedCreatedAt || new Date(user.createdAt).toLocaleDateString('zh-CN')}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editUser('${user._id}')" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-warning" onclick="regenerateAuthCode('${user._id}')" title="重新生成授权码">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteUser('${user._id}', '${user.username}')" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    
    container.innerHTML = table;
}

// 更新计数显示
function updateBrandCount() {
    document.getElementById('brandCount').textContent = brands.length;
}

function updateUserCount() {
    document.getElementById('userCount').textContent = users.length;
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loading = document.getElementById('loading');
    if (show) {
        loading.classList.add('show');
    } else {
        loading.classList.remove('show');
    }
}

// 显示提示消息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 品牌相关函数
function openAddBrandModal() {
    currentBrandId = null;
    uploadedIconFile = null;
    document.getElementById('brandModalTitle').textContent = '添加品牌';
    document.getElementById('brandForm').reset();
    document.getElementById('brandId').value = '';
    document.getElementById('brandIsActive').checked = true;
    document.getElementById('brandSortOrder').value = '0';

    // 重置文件上传组件
    if (brandIconUploader) {
        brandIconUploader.clear();
    }

    updateBrandIconPreview();
}

function editBrand(id) {
    const brand = brands.find(b => b._id === id);
    if (!brand) return;

    currentBrandId = id;
    uploadedIconFile = null;
    document.getElementById('brandModalTitle').textContent = '编辑品牌';
    document.getElementById('brandId').value = brand._id;
    document.getElementById('brandName').value = brand.name;
    document.getElementById('brandIcon').value = brand.icon;
    document.getElementById('brandDescription').value = brand.description || '';
    document.getElementById('brandSortOrder').value = brand.sortOrder;
    document.getElementById('brandIsActive').checked = brand.isActive;

    // 重置文件上传组件
    if (brandIconUploader) {
        brandIconUploader.clear();
    }

    updateBrandIconPreview();

    const modal = new bootstrap.Modal(document.getElementById('brandModal'));
    modal.show();
}

function updateBrandIconPreview() {
    const iconUrl = document.getElementById('brandIcon').value;
    const preview = document.getElementById('brandIconPreview');
    const placeholder = document.getElementById('brandIconPlaceholder');
    
    if (iconUrl) {
        preview.src = iconUrl;
        preview.style.display = 'block';
        placeholder.style.display = 'none';
        
        preview.onerror = function() {
            preview.style.display = 'none';
            placeholder.style.display = 'inline-flex';
        };
    } else {
        preview.style.display = 'none';
        placeholder.style.display = 'inline-flex';
    }
}

async function saveBrand() {
    const form = document.getElementById('brandForm');
    const iconUrl = document.getElementById('brandIcon').value.trim();
    const hasUploadedFile = brandIconUploader && brandIconUploader.getFiles().length > 0;

    // 检查是否有图标（URL或上传文件）
    if (!iconUrl && !hasUploadedFile && !uploadedIconFile) {
        showAlert('请提供品牌图标（URL或上传文件）', 'danger');
        return;
    }

    // 验证其他必填字段
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    try {
        // 如果有文件需要上传，先上传文件
        if (hasUploadedFile) {
            console.log('正在上传品牌图标...');
            await brandIconUploader.upload();
            // 上传成功后，uploadedIconFile会在onSuccess回调中设置
        }

        const brandData = {
            name: document.getElementById('brandName').value.trim(),
            icon: document.getElementById('brandIcon').value.trim(),
            description: document.getElementById('brandDescription').value.trim(),
            sortOrder: parseInt(document.getElementById('brandSortOrder').value) || 0,
            isActive: document.getElementById('brandIsActive').checked
        };

        // 如果有上传的文件，添加文件ID
        if (uploadedIconFile) {
            brandData.iconFileId = uploadedIconFile._id;
            // 如果没有URL，使用上传文件的URL
            if (!brandData.icon) {
                const baseUrl = API_BASE.replace('/api', '');
                brandData.icon = `${baseUrl}${uploadedIconFile.url}`;
            }
        }

        console.log('保存品牌数据:', brandData);
        console.log('当前品牌ID:', currentBrandId);

        // 获取当前用户信息（包含认证信息）
        const currentUser = await ipcRenderer.invoke('get-current-user');
        if (!currentUser || !currentUser.authCode) {
            showAlert('无法获取用户认证信息', 'danger');
            return;
        }

        const url = currentBrandId ? `${API_BASE}/brands/${currentBrandId}` : `${API_BASE}/brands`;
        const method = currentBrandId ? 'PUT' : 'POST';

        console.log('请求URL:', url);
        console.log('请求方法:', method);

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${currentUser.authCode}`
            },
            body: JSON.stringify(brandData)
        });
        
        const result = await response.json();
        console.log('品牌操作响应:', result);

        if (result.status === 'success') {
            showAlert(result.message, 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('brandModal'));
            modal.hide();
            console.log('品牌保存成功，重新加载品牌列表...');
            loadBrands(); // 重新加载列表
        } else {
            if (result.details) {
                showAlert('验证失败: ' + result.details.join(', '), 'danger');
            } else {
                showAlert(result.message || result.error, 'danger');
            }
        }
    } catch (error) {
        showAlert('保存失败: ' + error.message, 'danger');
    }
}

function deleteBrand(id, name) {
    deleteTarget = { type: 'brand', id: id, name: name };
    document.getElementById('deleteMessage').textContent = `确定要删除品牌 "${name}" 吗？`;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// 用户相关函数
function openAddUserModal() {
    currentUserId = null;
    document.getElementById('userModalTitle').textContent = '添加用户';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    document.getElementById('userType').value = 'user';
    document.getElementById('userIsActive').checked = true;
    generateAuthCode();
}

function editUser(id) {
    const user = users.find(u => u._id === id);
    if (!user) return;

    currentUserId = id;
    document.getElementById('userModalTitle').textContent = '编辑用户';
    document.getElementById('userId').value = user._id;
    document.getElementById('username').value = user.username;
    document.getElementById('email').value = user.email;
    document.getElementById('authCode').value = user.authCode;
    document.getElementById('displayName').value = user.displayName || '';
    document.getElementById('userType').value = user.userType;
    document.getElementById('userDescription').value = user.description || '';
    document.getElementById('userIsActive').checked = user.isActive;

    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

function generateAuthCode() {
    // 生成随机授权码
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('authCode').value = result;
}

async function saveUser() {
    const form = document.getElementById('userForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const userData = {
        username: document.getElementById('username').value.trim(),
        email: document.getElementById('email').value.trim(),
        authCode: document.getElementById('authCode').value.trim(),
        displayName: document.getElementById('displayName').value.trim(),
        userType: document.getElementById('userType').value,
        description: document.getElementById('userDescription').value.trim(),
        isActive: document.getElementById('userIsActive').checked
    };

    try {
        console.log('保存用户数据:', userData);
        console.log('当前用户ID:', currentUserId);

        // 获取当前用户信息（包含认证信息）
        const currentUser = await ipcRenderer.invoke('get-current-user');
        if (!currentUser || !currentUser.authCode) {
            showAlert('无法获取用户认证信息', 'danger');
            return;
        }

        const url = currentUserId ? `${API_BASE}/users/${currentUserId}` : `${API_BASE}/users`;
        const method = currentUserId ? 'PUT' : 'POST';

        console.log('请求URL:', url);
        console.log('请求方法:', method);

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${currentUser.authCode}`
            },
            body: JSON.stringify(userData)
        });

        const result = await response.json();
        console.log('用户保存响应:', result);

        if (result.status === 'success') {
            showAlert(result.message, 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();
            console.log('用户保存成功，重新加载用户列表...');
            loadUsers(); // 重新加载列表
        } else {
            if (result.details) {
                showAlert('验证失败: ' + result.details.join(', '), 'danger');
            } else {
                showAlert(result.message || result.error, 'danger');
            }
        }
    } catch (error) {
        console.error('保存用户失败:', error);
        showAlert('保存失败: ' + error.message, 'danger');
    }
}

async function regenerateAuthCode(id) {
    try {
        console.log('重新生成授权码，用户ID:', id);

        // 获取当前用户信息（包含认证信息）
        const currentUser = await ipcRenderer.invoke('get-current-user');
        if (!currentUser || !currentUser.authCode) {
            showAlert('无法获取用户认证信息', 'danger');
            return;
        }

        const response = await fetch(`${API_BASE}/users/${id}/regenerate-auth-code`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${currentUser.authCode}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        console.log('重新生成授权码响应:', result);

        if (result.status === 'success') {
            showAlert('授权码重新生成成功', 'success');
            loadUsers(); // 重新加载列表
        } else {
            showAlert(result.message || result.error, 'danger');
        }
    } catch (error) {
        showAlert('重新生成授权码失败: ' + error.message, 'danger');
    }
}

function deleteUser(id, name) {
    deleteTarget = { type: 'user', id: id, name: name };
    document.getElementById('deleteMessage').textContent = `确定要删除用户 "${name}" 吗？`;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// 确认删除
async function confirmDelete() {
    if (!deleteTarget.id) return;

    try {
        console.log('删除操作:', deleteTarget);

        // 获取当前用户信息（包含认证信息）
        const currentUser = await ipcRenderer.invoke('get-current-user');
        if (!currentUser || !currentUser.authCode) {
            showAlert('无法获取用户认证信息', 'danger');
            return;
        }

        const url = deleteTarget.type === 'brand'
            ? `${API_BASE}/brands/${deleteTarget.id}`
            : `${API_BASE}/users/${deleteTarget.id}`;

        console.log('删除请求URL:', url);

        const response = await fetch(url, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${currentUser.authCode}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        console.log('删除响应:', result);

        if (result.status === 'success') {
            showAlert(result.message, 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();

            // 重新加载对应的列表
            if (deleteTarget.type === 'brand') {
                loadBrands();
            } else {
                loadUsers();
            }
        } else {
            showAlert(result.error, 'danger');
        }
    } catch (error) {
        showAlert('删除失败: ' + error.message, 'danger');
    } finally {
        deleteTarget = { type: '', id: '', name: '' };
    }
}

// 文件管理相关函数
let files = [];
let filteredFiles = [];

// 加载文件列表
async function loadFiles() {
    try {
        console.log('开始加载文件列表...');

        // 获取当前用户信息（包含认证信息）
        const currentUser = await ipcRenderer.invoke('get-current-user');
        if (!currentUser || !currentUser.authCode) {
            console.warn('无法获取用户认证信息');
            return;
        }

        const response = await fetch(`${API_BASE}/files?limit=100`, {
            headers: {
                'Authorization': `Bearer ${currentUser.authCode}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        console.log('文件API响应:', result);

        if (result.status === 'success') {
            files = result.data.map(file => {
                // 处理文件数据，添加必要的属性
                return {
                    ...file,
                    // 判断是否为图片文件
                    isImage: file.fileType === 'image' || (file.mimetype && file.mimetype.startsWith('image/')),
                    // 确保URL格式正确
                    url: file.url || `/uploads/${file.filename}`,
                    // 格式化文件大小
                    formattedSize: formatFileSize(file.size),
                    // 格式化创建时间
                    formattedCreatedAt: formatDate(file.createdAt)
                };
            });
            filteredFiles = [...files];
            renderFileList();
            updateFileStats();
        } else {
            console.error('加载文件列表失败:', result.message);
        }
    } catch (error) {
        console.error('加载文件列表失败:', error);
    }
}

// 更新文件统计信息
async function updateFileStats() {
    try {
        const currentUser = await ipcRenderer.invoke('get-current-user');
        if (!currentUser || !currentUser.authCode) return;

        const response = await fetch(`${API_BASE}/files/stats`, {
            headers: {
                'Authorization': `Bearer ${currentUser.authCode}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        if (result.status === 'success') {
            const stats = result.data;

            document.getElementById('totalFiles').textContent = stats.totalFiles || 0;
            document.getElementById('totalSize').textContent = formatFileSize(stats.totalSize || 0);

            // 统计不同类型的文件
            const imageCount = stats.byType.find(t => t._id === 'image')?.count || 0;
            const otherCount = stats.totalFiles - imageCount;

            document.getElementById('imageFiles').textContent = imageCount;
            document.getElementById('otherFiles').textContent = otherCount;
        }
    } catch (error) {
        console.error('获取文件统计失败:', error);
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 渲染文件列表
function renderFileList() {
    const fileListContainer = document.getElementById('fileList');
    const fileCountElement = document.getElementById('fileCount');

    if (!fileListContainer) return;

    fileCountElement.textContent = filteredFiles.length;

    if (filteredFiles.length === 0) {
        fileListContainer.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-files text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-2">${i18nTexts.admin?.file?.messages?.no_files || '暂无文件'}</p>
            </div>
        `;
        return;
    }

    const tableHTML = `
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>${i18nTexts.admin?.table?.preview || '预览'}</th>
                    <th>${i18nTexts.admin?.table?.file_name || '文件名'}</th>
                    <th>${i18nTexts.admin?.table?.file_type || '类型'}</th>
                    <th>${i18nTexts.admin?.table?.file_size || '大小'}</th>
                    <th>${i18nTexts.admin?.table?.uploader || '上传者'}</th>
                    <th>${i18nTexts.admin?.table?.upload_time || '上传时间'}</th>
                    <th>${i18nTexts.admin?.table?.actions || '操作'}</th>
                </tr>
            </thead>
            <tbody>
                ${filteredFiles.map(file => `
                    <tr>
                        <td>
                            <div class="file-preview-container">
                                ${generateFilePreview(file)}
                            </div>
                        </td>
                        <td>
                            <div class="file-name" title="${file.originalName}">
                                ${file.originalName.length > 30 ? file.originalName.substring(0, 30) + '...' : file.originalName}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-${getFileTypeBadgeColor(file.fileType)}">${getFileTypeDisplay(file.fileType)}</span>
                        </td>
                        <td>${file.formattedSize}</td>
                        <td>${file.uploadedBy?.displayName || file.uploadedBy?.username || '未知'}</td>
                        <td>${file.formattedCreatedAt}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="viewFile('${file._id}')" title="${i18nTexts.admin?.file?.actions?.view || '查看'}">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="downloadFile('${file._id}')" title="${i18nTexts.admin?.file?.actions?.download || '下载'}">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteFile('${file._id}', '${file.originalName}')" title="${i18nTexts.admin?.file?.actions?.delete || '删除'}">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    fileListContainer.innerHTML = tableHTML;
}

// 获取文件类型显示名称
function getFileTypeDisplay(fileType) {
    const typeMap = {
        'image': i18nTexts.admin?.file?.filters?.image || '图片',
        'document': i18nTexts.admin?.file?.filters?.document || '文档',
        'video': i18nTexts.admin?.file?.filters?.video || '视频',
        'audio': i18nTexts.admin?.file?.filters?.audio || '音频',
        'other': i18nTexts.admin?.file?.filters?.other || '其他'
    };
    return typeMap[fileType] || '未知';
}

// 获取文件类型徽章颜色
function getFileTypeBadgeColor(fileType) {
    const colorMap = {
        'image': 'success',
        'document': 'primary',
        'video': 'warning',
        'audio': 'info',
        'other': 'secondary'
    };
    return colorMap[fileType] || 'secondary';
}

// 生成文件预览
function generateFilePreview(file) {
    const baseUrl = API_BASE.replace('/api', '');

    if (file.isImage || file.fileType === 'image') {
        // 图片预览
        const imageUrl = file.url ? `${baseUrl}${file.url}` : `${baseUrl}/uploads/${file.filename}`;
        return `
            <div class="image-preview-wrapper">
                <img src="${imageUrl}"
                     class="file-thumbnail"
                     alt="${file.originalName}"
                     onclick="previewImage('${imageUrl}', '${file.originalName}')"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                     onload="this.nextElementSibling.style.display='none';">
                <div class="preview-fallback" style="display: none;">
                    <i class="bi bi-image text-muted"></i>
                </div>
            </div>
        `;
    } else {
        // 非图片文件的图标预览
        const iconClass = getFileTypeIcon(file.fileType, file.mimetype);
        const iconColor = getFileTypeIconColor(file.fileType);
        return `
            <div class="file-icon-preview" onclick="viewFile('${file._id}')">
                <i class="bi ${iconClass} ${iconColor}"></i>
            </div>
        `;
    }
}

// 获取文件类型图标
function getFileTypeIcon(fileType, mimetype) {
    // 根据文件类型返回对应的Bootstrap图标
    switch (fileType) {
        case 'image':
            return 'bi-image';
        case 'document':
            if (mimetype && mimetype.includes('pdf')) {
                return 'bi-file-pdf';
            } else if (mimetype && (mimetype.includes('word') || mimetype.includes('document'))) {
                return 'bi-file-word';
            } else if (mimetype && (mimetype.includes('excel') || mimetype.includes('spreadsheet'))) {
                return 'bi-file-excel';
            } else if (mimetype && (mimetype.includes('powerpoint') || mimetype.includes('presentation'))) {
                return 'bi-file-ppt';
            } else {
                return 'bi-file-text';
            }
        case 'video':
            return 'bi-file-play';
        case 'audio':
            return 'bi-file-music';
        case 'other':
        default:
            if (mimetype && mimetype.includes('zip')) {
                return 'bi-file-zip';
            } else if (mimetype && mimetype.includes('json')) {
                return 'bi-file-code';
            } else {
                return 'bi-file-earmark';
            }
    }
}

// 获取文件类型图标颜色
function getFileTypeIconColor(fileType) {
    const colorMap = {
        'image': 'text-success',
        'document': 'text-primary',
        'video': 'text-warning',
        'audio': 'text-info',
        'other': 'text-muted'
    };
    return colorMap[fileType] || 'text-muted';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '未知';

    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
        return '今天';
    } else if (diffDays === 2) {
        return '昨天';
    } else if (diffDays <= 7) {
        return `${diffDays - 1}天前`;
    } else {
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }
}

// 文件过滤
function filterFiles() {
    const typeFilter = document.getElementById('fileTypeFilter').value;
    const searchInput = document.getElementById('fileSearchInput').value.toLowerCase();

    filteredFiles = files.filter(file => {
        const matchesType = !typeFilter || file.fileType === typeFilter;
        const matchesSearch = !searchInput || file.originalName.toLowerCase().includes(searchInput);
        return matchesType && matchesSearch;
    });

    renderFileList();
}

// 搜索文件
function searchFiles() {
    filterFiles();
}

// 排序文件
function sortFiles() {
    const sortOrder = document.getElementById('fileSortOrder').value;

    filteredFiles.sort((a, b) => {
        switch (sortOrder) {
            case 'newest':
                return new Date(b.createdAt) - new Date(a.createdAt);
            case 'oldest':
                return new Date(a.createdAt) - new Date(b.createdAt);
            case 'largest':
                return b.size - a.size;
            case 'smallest':
                return a.size - b.size;
            case 'name':
                return a.originalName.localeCompare(b.originalName);
            default:
                return 0;
        }
    });

    renderFileList();
}

// 刷新文件列表
function refreshFileList() {
    loadFiles();
}

// 预览图片
function previewImage(imageUrl, fileName) {
    // 创建模态框显示大图
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${fileName}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="${imageUrl}" class="img-fluid" alt="${fileName}" style="max-height: 70vh;">
                </div>
                <div class="modal-footer">
                    <a href="${imageUrl}" target="_blank" class="btn btn-primary">
                        <i class="bi bi-download me-1"></i>${i18nTexts.admin?.file?.actions?.download || '下载'}
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${i18nTexts.common?.close || '关闭'}</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 模态框关闭后移除元素
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// 查看文件
function viewFile(fileId) {
    const file = files.find(f => f._id === fileId);
    if (!file) return;

    // 从API_BASE中提取基础URL
    const baseUrl = API_BASE.replace('/api', '');
    const fileUrl = file.url ? `${baseUrl}${file.url}` : `${baseUrl}/uploads/${file.filename}`;

    if (file.isImage) {
        // 如果是图片，使用预览功能
        previewImage(fileUrl, file.originalName);
    } else {
        // 非图片文件下载
        downloadFile(fileId);
    }
}

// 下载文件
async function downloadFile(fileId) {
    try {
        const currentUser = await ipcRenderer.invoke('get-current-user');
        if (!currentUser || !currentUser.authCode) {
            showAlert('无法获取用户认证信息', 'danger');
            return;
        }

        // 获取文件信息
        const file = files.find(f => f._id === fileId);
        if (!file) {
            showAlert('文件不存在', 'danger');
            return;
        }

        // 使用fetch下载文件
        const response = await fetch(`${API_BASE}/files/${fileId}/download`, {
            headers: {
                'Authorization': `Bearer ${currentUser.authCode}`
            }
        });

        if (!response.ok) {
            throw new Error(`下载失败: ${response.status}`);
        }

        // 获取文件blob
        const blob = await response.blob();

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = file.originalName || file.filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理URL对象
        window.URL.revokeObjectURL(url);

    } catch (error) {
        console.error('下载文件失败:', error);
        showAlert('下载文件失败: ' + error.message, 'danger');
    }
}

// 删除文件
function deleteFile(fileId, fileName) {
    const confirmMessage = i18nTexts.admin?.file?.messages?.delete_confirm?.replace('{fileName}', fileName) || `确定要删除文件 "${fileName}" 吗？此操作不可恢复！`;
    if (confirm(confirmMessage)) {
        performFileDelete(fileId);
    }
}

// 执行文件删除
async function performFileDelete(fileId) {
    try {
        const currentUser = await ipcRenderer.invoke('get-current-user');
        if (!currentUser || !currentUser.authCode) {
            showAlert('无法获取用户认证信息', 'danger');
            return;
        }

        const response = await fetch(`${API_BASE}/files/${fileId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${currentUser.authCode}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.status === 'success') {
            showAlert(i18nTexts.admin?.file?.messages?.delete_success || '文件删除成功', 'success');
            loadFiles(); // 重新加载文件列表
        } else {
            showAlert((i18nTexts.admin?.file?.messages?.delete_failed || '删除失败') + ': ' + (result.message || result.error), 'danger');
        }
    } catch (error) {
        console.error('删除文件失败:', error);
        showAlert('删除文件失败: ' + error.message, 'danger');
    }
}

// 清理重复文件
async function cleanupFiles() {
    const confirmMessage = i18nTexts.admin?.file?.messages?.cleanup_confirm || '确定要清理重复文件吗？这将删除所有重复的文件，只保留最早上传的版本。';
    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const currentUser = await ipcRenderer.invoke('get-current-user');
        if (!currentUser || !currentUser.authCode) {
            showAlert('无法获取用户认证信息', 'danger');
            return;
        }

        // 显示加载状态
        showAlert('正在清理重复文件，请稍候...', 'info');

        const response = await fetch(`${API_BASE}/files/cleanup-duplicates`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${currentUser.authCode}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        console.log('清理重复文件响应:', result);

        if (result.status === 'success') {
            showAlert(result.message, 'success');
            // 重新加载文件列表和统计信息
            loadFiles();
            updateFileStats();
        } else {
            showAlert('清理失败: ' + (result.message || result.error), 'danger');
        }

    } catch (error) {
        console.error('清理文件失败:', error);
        showAlert('清理文件失败: ' + error.message, 'danger');
    }
}
