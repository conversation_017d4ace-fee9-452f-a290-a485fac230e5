/**
 * 用户模型
 * 定义用户数据结构和业务逻辑
 */

const mongoose = require('mongoose');
const crypto = require('crypto');
const { USER_TYPES, USER_TYPE_NAMES, VALIDATION_RULES } = require('../constants');

/**
 * 用户 Schema 定义
 */
const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, '用户名是必需的'],
    unique: true,
    trim: true,
    minlength: [VALIDATION_RULES.USER.USERNAME.MIN_LENGTH, `用户名至少需要 ${VALIDATION_RULES.USER.USERNAME.MIN_LENGTH} 个字符`],
    maxlength: [VALIDATION_RULES.USER.USERNAME.MAX_LENGTH, `用户名不能超过 ${VALIDATION_RULES.USER.USERNAME.MAX_LENGTH} 个字符`],
    validate: {
      validator: function(v) {
        return VALIDATION_RULES.USER.USERNAME.PATTERN.test(v);
      },
      message: '用户名只能包含英文、数字、连字符和下划线'
    }
  },
  
  email: {
    type: String,
    trim: true,
    lowercase: true,
    sparse: true, // 允许多个文档有 null 值
    validate: {
      validator: function(v) {
        return !v || VALIDATION_RULES.USER.EMAIL.PATTERN.test(v);
      },
      message: '请提供有效的邮箱地址'
    }
  },
  
  authCode: {
    type: String,
    required: [true, '授权码是必需的'],
    unique: true,
    trim: true,
    minlength: [VALIDATION_RULES.USER.AUTH_CODE.MIN_LENGTH, `授权码至少需要 ${VALIDATION_RULES.USER.AUTH_CODE.MIN_LENGTH} 个字符`],
    maxlength: [VALIDATION_RULES.USER.AUTH_CODE.MAX_LENGTH, `授权码不能超过 ${VALIDATION_RULES.USER.AUTH_CODE.MAX_LENGTH} 个字符`]
  },
  
  userType: {
    type: String,
    enum: Object.values(USER_TYPES),
    default: USER_TYPES.USER,
    required: true,
    index: true
  },
  
  displayName: {
    type: String,
    trim: true,
    maxlength: [100, '显示名称不能超过100个字符']
  },
  
  avatar: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        return !v || /^https?:\/\/.+\.(jpg|jpeg|png|gif|svg|webp)$/i.test(v);
      },
      message: '请提供有效的头像URL'
    }
  },
  
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  
  lastLoginAt: {
    type: Date
  },
  
  loginCount: {
    type: Number,
    default: 0
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: [500, '描述不能超过500个字符']
  },
  
  preferences: {
    language: {
      type: String,
      default: 'zh-CN'
    },
    theme: {
      type: String,
      default: 'auto'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      system: {
        type: Boolean,
        default: true
      }
    }
  },
  
  metadata: {
    lastIP: String,
    userAgent: String,
    loginHistory: [{
      timestamp: {
        type: Date,
        default: Date.now
      },
      ip: String,
      userAgent: String
    }]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

/**
 * 索引定义
 */
userSchema.index({ username: 1 }, { unique: true });
userSchema.index({ email: 1 }, { sparse: true });
userSchema.index({ authCode: 1 }, { unique: true });
userSchema.index({ userType: 1, isActive: 1 });
userSchema.index({ lastLoginAt: -1 });
userSchema.index({ createdAt: -1 });

/**
 * 虚拟字段
 */

// 格式化创建时间
userSchema.virtual('formattedCreatedAt').get(function() {
  return this.createdAt ? this.createdAt.toLocaleDateString('zh-CN') : null;
});

// 格式化更新时间
userSchema.virtual('formattedUpdatedAt').get(function() {
  return this.updatedAt ? this.updatedAt.toLocaleDateString('zh-CN') : null;
});

// 格式化最后登录时间
userSchema.virtual('formattedLastLoginAt').get(function() {
  return this.lastLoginAt ? this.lastLoginAt.toLocaleDateString('zh-CN') : '从未登录';
});

// 用户类型显示名
userSchema.virtual('userTypeDisplay').get(function() {
  return USER_TYPE_NAMES[this.userType] || this.userType;
});

// 是否在线（最近5分钟内登录）
userSchema.virtual('isOnline').get(function() {
  if (!this.lastLoginAt) return false;
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return this.lastLoginAt > fiveMinutesAgo;
});

/**
 * 实例方法
 */

// 生成随机授权码
userSchema.methods.generateAuthCode = function() {
  return crypto.randomBytes(16).toString('hex').toUpperCase();
};

// 更新登录信息
userSchema.methods.updateLoginInfo = function(ip, userAgent) {
  this.lastLoginAt = new Date();
  this.loginCount += 1;
  
  if (ip) this.metadata.lastIP = ip;
  if (userAgent) this.metadata.userAgent = userAgent;
  
  // 记录登录历史（保留最近10次）
  this.metadata.loginHistory.unshift({
    timestamp: new Date(),
    ip,
    userAgent
  });
  
  if (this.metadata.loginHistory.length > 10) {
    this.metadata.loginHistory = this.metadata.loginHistory.slice(0, 10);
  }
  
  return this.save();
};

// 检查是否为管理员
userSchema.methods.isAdmin = function() {
  return this.userType === USER_TYPES.ADMIN;
};

// 检查是否为普通用户
userSchema.methods.isUser = function() {
  return this.userType === USER_TYPES.USER;
};

// 激活用户
userSchema.methods.activate = function() {
  this.isActive = true;
  return this.save();
};

// 停用用户
userSchema.methods.deactivate = function() {
  this.isActive = false;
  return this.save();
};

// 更新偏好设置
userSchema.methods.updatePreferences = function(preferences) {
  this.preferences = { ...this.preferences, ...preferences };
  return this.save();
};

// 重置授权码
userSchema.methods.resetAuthCode = function() {
  this.authCode = this.generateAuthCode();
  return this.save();
};

// 安全的JSON输出（不包含敏感信息）
userSchema.methods.toSafeJSON = function() {
  const user = this.toObject();
  
  // 移除敏感信息
  delete user.authCode;
  delete user.metadata.lastIP;
  delete user.metadata.userAgent;
  delete user.metadata.loginHistory;
  
  return {
    ...user,
    formattedCreatedAt: this.formattedCreatedAt,
    formattedUpdatedAt: this.formattedUpdatedAt,
    formattedLastLoginAt: this.formattedLastLoginAt,
    userTypeDisplay: this.userTypeDisplay,
    isOnline: this.isOnline
  };
};

// 管理员视图的JSON输出（包含更多信息）
userSchema.methods.toAdminJSON = function() {
  const user = this.toObject();
  
  return {
    ...user,
    formattedCreatedAt: this.formattedCreatedAt,
    formattedUpdatedAt: this.formattedUpdatedAt,
    formattedLastLoginAt: this.formattedLastLoginAt,
    userTypeDisplay: this.userTypeDisplay,
    isOnline: this.isOnline
  };
};

/**
 * 静态方法
 */

// 通过授权码查找用户
userSchema.statics.findByAuthCode = function(authCode) {
  return this.findOne({ authCode: authCode, isActive: true });
};

// 获取活跃用户
userSchema.statics.getActiveUsers = function(options = {}) {
  const { limit, skip, sortBy = 'createdAt', sortOrder = -1 } = options;
  
  let query = this.find({ isActive: true });
  
  // 排序
  const sort = {};
  sort[sortBy] = sortOrder;
  query = query.sort(sort);
  
  // 分页
  if (skip) query = query.skip(skip);
  if (limit) query = query.limit(limit);
  
  return query;
};

// 获取管理员用户
userSchema.statics.getAdminUsers = function(options = {}) {
  const { limit, skip } = options;
  
  let query = this.find({ userType: USER_TYPES.ADMIN, isActive: true });
  
  if (skip) query = query.skip(skip);
  if (limit) query = query.limit(limit);
  
  return query.sort({ createdAt: -1 });
};

// 按用户类型获取用户
userSchema.statics.getByUserType = function(userType, options = {}) {
  const { limit, skip, includeInactive = false } = options;
  
  const query = { userType };
  if (!includeInactive) {
    query.isActive = true;
  }
  
  let mongoQuery = this.find(query);
  
  if (skip) mongoQuery = mongoQuery.skip(skip);
  if (limit) mongoQuery = mongoQuery.limit(limit);
  
  return mongoQuery.sort({ createdAt: -1 });
};

// 搜索用户
userSchema.statics.search = function(keyword, options = {}) {
  const { userType, isActive, limit, skip } = options;
  
  const searchRegex = new RegExp(keyword, 'i');
  const query = {
    $or: [
      { username: searchRegex },
      { displayName: searchRegex },
      { email: searchRegex },
      { description: searchRegex }
    ]
  };
  
  if (userType) query.userType = userType;
  if (isActive !== undefined) query.isActive = isActive;
  
  let mongoQuery = this.find(query);
  
  if (skip) mongoQuery = mongoQuery.skip(skip);
  if (limit) mongoQuery = mongoQuery.limit(limit);
  
  return mongoQuery.sort({ lastLoginAt: -1, createdAt: -1 });
};

// 获取在线用户
userSchema.statics.getOnlineUsers = function() {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return this.find({
    isActive: true,
    lastLoginAt: { $gte: fiveMinutesAgo }
  }).sort({ lastLoginAt: -1 });
};

// 获取统计信息
userSchema.statics.getStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: {
          userType: '$userType',
          isActive: '$isActive'
        },
        count: { $sum: 1 },
        totalLogins: { $sum: '$loginCount' }
      }
    }
  ]);
};

/**
 * 中间件
 */

// 保存前处理
userSchema.pre('save', function(next) {
  // 如果没有显示名称，使用用户名
  if (!this.displayName) {
    this.displayName = this.username;
  }
  
  // 如果是新用户且没有授权码，自动生成
  if (this.isNew && !this.authCode) {
    this.authCode = this.generateAuthCode();
  }
  
  // 确保用户名唯一性（忽略大小写）
  if (this.isModified('username')) {
    this.username = this.username.trim().toLowerCase();
  }
  
  // 处理邮箱
  if (this.isModified('email') && this.email) {
    this.email = this.email.trim().toLowerCase();
  }
  
  next();
});

// 删除前处理
userSchema.pre('remove', function(next) {
  // 这里可以添加删除前的清理逻辑
  // 比如删除用户相关的数据、文件等
  next();
});

/**
 * 创建模型
 */
const User = mongoose.model('User', userSchema);

module.exports = User;
