# 域名配置修改说明

## 概述

本次修改解决了客户端代码中硬编码 `localhost:3000` 的问题，现在所有的API请求都会使用 `.env` 文件中配置的域名。

## 修改的文件

### 1. 主进程文件
- **文件**: `src/main/main.js`
- **修改**: 第248-258行，授权验证API调用现在使用环境配置的域名
- **变更**: 
  ```javascript
  // 修改前
  url: 'http://localhost:3000/api/users/auth'
  
  // 修改后
  const { getApiBaseUrl } = require('../shared/config/env');
  const apiBaseUrl = getApiBaseUrl();
  url: `${apiBaseUrl}/api/users/auth`
  ```

### 2. 管理页面脚本
- **文件**: `src/renderer/pages/admin.js`
- **修改**: 
  1. 第14-30行：将硬编码的API_BASE替换为动态获取
  2. 第32-48行：在页面初始化时调用API配置获取
  3. 第1222-1238行：文件查看功能使用动态域名
  4. 新增CSP动态更新功能

### 3. 文件上传组件
- **文件**: `src/renderer/components/FileUpload.js`
- **修改**: 
  1. 第6-25行：构造函数中不再硬编码uploadUrl
  2. 第27-55行：新增动态获取API配置的方法

### 4. HTML文件CSP策略
- **文件**: `src/renderer/pages/login.html`
- **修改**: 第6行，CSP策略改为通用的http/https协议支持

- **文件**: `src/renderer/pages/admin.html`  
- **修改**: 第7行，CSP策略改为通用的http/https协议支持

### 5. 登录页面脚本
- **文件**: `src/renderer/pages/login.js`
- **修改**: 第3-34行，新增动态CSP更新功能

### 6. API配置模块
- **文件**: `src/shared/config/api.js`
- **修改**: 第114行，新增getConfig导出函数

## 新增功能

### 1. 动态API基础URL获取
所有需要调用API的地方现在都会：
1. 通过IPC调用获取当前环境的API配置
2. 使用配置中的BASE_URL而不是硬编码的localhost:3000
3. 如果获取失败，回退到默认的localhost:3000

### 2. 动态CSP策略更新
为了支持不同域名的API调用，页面会：
1. 在加载时动态获取API配置
2. 更新CSP策略以包含当前配置的API域名
3. 确保网络请求不被CSP阻止

## 配置方法

### 开发环境
修改 `client/.env` 文件中的 `API_BASE_URL_DEV` 值：
```env
API_BASE_URL_DEV=http://your-api-domain.com:port
```

### 测试环境
修改 `client/.env` 文件中的 `API_BASE_URL_TEST` 值：
```env
API_BASE_URL_TEST=http://test-api.your-domain.com
```

### 生产环境
修改 `client/.env` 文件中的 `API_BASE_URL_PROD` 值：
```env
API_BASE_URL_PROD=https://api.your-domain.com
```

## 验证方法

1. 修改 `.env` 文件中的API域名
2. 重启应用
3. 检查网络请求是否使用了新的域名
4. 确认所有功能（登录、管理、文件上传等）正常工作

## 错误处理机制

### 配置获取失败时的处理
- **文件上传组件**: 显示错误提示，告知用户功能不可用
- **管理页面**: 显示配置错误警告，禁用依赖API的功能
- **主进程**: 抛出错误，不再回退到硬编码值

### 用户体验
- 明确的错误提示信息
- 指导用户如何解决配置问题
- 部分功能仍可正常使用（如主题切换、语言切换）

## 配置要求

**重要**: 现在必须正确配置 `.env` 文件，系统不再提供硬编码的回退值。

## 注意事项

1. **必需配置**: `.env` 文件必须存在且配置正确，系统不再提供硬编码回退
2. **CORS配置**: 确保后端服务器的CORS配置允许新域名的请求
3. **SSL证书**: 如果使用HTTPS，确保SSL证书配置正确
4. **防火墙**: 确保网络防火墙允许新端口的访问
5. **环境变量**: 确保 `.env` 文件在正确的位置且格式正确
6. **API服务**: 确保API服务器在配置的地址上正常运行

## 测试完成

✅ 环境变量正确读取
✅ API配置动态获取
✅ 主进程授权验证使用动态域名
✅ 渲染进程API调用使用动态域名
✅ 文件上传使用动态域名
✅ CSP策略动态更新
✅ 错误处理机制完善
✅ 用户友好的错误提示
✅ 配置失败时的功能降级
