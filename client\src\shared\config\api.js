/**
 * API 配置文件
 * 统一管理 API 基础配置和环境变量
 */

// 导入环境变量配置
const { APP_CONFIG, getCurrentEnvironment, getApiBaseUrl } = require('./env');

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
};

// 当前环境
const CURRENT_ENV = getCurrentEnvironment();

// API 基础配置
const API_CONFIG = {
  // 开发环境配置
  [ENV.DEVELOPMENT]: {
    BASE_URL: APP_CONFIG.API_BASE_URL_DEV,
    TIMEOUT: APP_CONFIG.API_TIMEOUT,
    RETRY_TIMES: APP_CONFIG.API_RETRY_TIMES
  },

  // 生产环境配置
  [ENV.PRODUCTION]: {
    BASE_URL: APP_CONFIG.API_BASE_URL_PROD,
    TIMEOUT: APP_CONFIG.API_TIMEOUT,
    RETRY_TIMES: APP_CONFIG.API_RETRY_TIMES
  },

  // 测试环境配置
  [ENV.TEST]: {
    BASE_URL: APP_CONFIG.API_BASE_URL_TEST,
    TIMEOUT: APP_CONFIG.API_TIMEOUT,
    RETRY_TIMES: APP_CONFIG.API_RETRY_TIMES
  }
};

// 获取当前环境的配置
const getCurrentConfig = () => {
  return API_CONFIG[CURRENT_ENV] || API_CONFIG[ENV.DEVELOPMENT];
};

// 导出配置
const config = getCurrentConfig();

// API 端点配置
const API_ENDPOINTS = {
  // 基础路径
  BASE: '/api',
  
  // 品牌相关接口
  BRANDS: {
    LIST: '/api/brands',
    DETAIL: (id) => `/api/brands/${id}`,
    CREATE: '/api/brands',
    UPDATE: (id) => `/api/brands/${id}`,
    DELETE: (id) => `/api/brands/${id}`
  },
  
  // 用户相关接口
  USERS: {
    LIST: '/api/users',
    DETAIL: (id) => `/api/users/${id}`,
    CREATE: '/api/users',
    UPDATE: (id) => `/api/users/${id}`,
    DELETE: (id) => `/api/users/${id}`,
    AUTH: '/api/users/auth',
    REGENERATE_AUTH: (id) => `/api/users/${id}/regenerate-auth`
  },
  
  // 系统信息接口
  SYSTEM: {
    INFO: '/',
    DOCS: '/api-docs'
  }
};

// HTTP 状态码
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
};

// 请求头配置
const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};

module.exports = {
  ENV,
  CURRENT_ENV,
  API_CONFIG,
  config,
  API_ENDPOINTS,
  HTTP_STATUS,
  DEFAULT_HEADERS,

  // 辅助函数
  getBaseUrl: () => config.BASE_URL,
  getTimeout: () => config.TIMEOUT,
  getRetryTimes: () => config.RETRY_TIMES,
  getConfig: () => config,

  // 构建完整的 API URL
  buildUrl: (endpoint) => {
    const baseUrl = config.BASE_URL.replace(/\/$/, ''); // 移除末尾斜杠
    const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${baseUrl}${path}`;
  },

  // 检查是否为开发环境
  isDevelopment: () => CURRENT_ENV === ENV.DEVELOPMENT,

  // 检查是否为生产环境
  isProduction: () => CURRENT_ENV === ENV.PRODUCTION,

  // 检查是否为测试环境
  isTest: () => CURRENT_ENV === ENV.TEST
};
