/**
 * API 服务
 * 封装所有 API 调用，提供统一的接口
 */

const httpService = require('./http');
const { API_ENDPOINTS } = require('../../shared/config/api');

class ApiService {
  constructor() {
    this.http = httpService;
  }

  // ==================== 品牌管理 API ====================

  /**
   * 获取品牌列表
   * @param {object} params - 查询参数
   * @returns {Promise} 品牌列表
   */
  async getBrands(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${API_ENDPOINTS.BRANDS.LIST}?${queryString}` : API_ENDPOINTS.BRANDS.LIST;
    return this.http.get(url);
  }

  /**
   * 获取单个品牌
   * @param {string} id - 品牌 ID
   * @returns {Promise} 品牌详情
   */
  async getBrand(id) {
    return this.http.get(API_ENDPOINTS.BRANDS.DETAIL(id));
  }

  /**
   * 创建品牌
   * @param {object} brandData - 品牌数据
   * @returns {Promise} 创建结果
   */
  async createBrand(brandData) {
    return this.http.post(API_ENDPOINTS.BRANDS.CREATE, brandData);
  }

  /**
   * 更新品牌
   * @param {string} id - 品牌 ID
   * @param {object} brandData - 品牌数据
   * @returns {Promise} 更新结果
   */
  async updateBrand(id, brandData) {
    return this.http.put(API_ENDPOINTS.BRANDS.UPDATE(id), brandData);
  }

  /**
   * 删除品牌
   * @param {string} id - 品牌 ID
   * @returns {Promise} 删除结果
   */
  async deleteBrand(id) {
    return this.http.delete(API_ENDPOINTS.BRANDS.DELETE(id));
  }

  // ==================== 用户管理 API ====================

  /**
   * 获取用户列表
   * @param {object} params - 查询参数
   * @returns {Promise} 用户列表
   */
  async getUsers(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${API_ENDPOINTS.USERS.LIST}?${queryString}` : API_ENDPOINTS.USERS.LIST;
    return this.http.get(url);
  }

  /**
   * 获取单个用户
   * @param {string} id - 用户 ID
   * @returns {Promise} 用户详情
   */
  async getUser(id) {
    return this.http.get(API_ENDPOINTS.USERS.DETAIL(id));
  }

  /**
   * 创建用户
   * @param {object} userData - 用户数据
   * @returns {Promise} 创建结果
   */
  async createUser(userData) {
    return this.http.post(API_ENDPOINTS.USERS.CREATE, userData);
  }

  /**
   * 更新用户
   * @param {string} id - 用户 ID
   * @param {object} userData - 用户数据
   * @returns {Promise} 更新结果
   */
  async updateUser(id, userData) {
    return this.http.put(API_ENDPOINTS.USERS.UPDATE(id), userData);
  }

  /**
   * 删除用户
   * @param {string} id - 用户 ID
   * @returns {Promise} 删除结果
   */
  async deleteUser(id) {
    return this.http.delete(API_ENDPOINTS.USERS.DELETE(id));
  }

  /**
   * 用户授权验证
   * @param {string} authCode - 授权码
   * @returns {Promise} 验证结果
   */
  async authenticateUser(authCode) {
    return this.http.post(API_ENDPOINTS.USERS.AUTH, { authCode });
  }

  /**
   * 重新生成用户授权码
   * @param {string} id - 用户 ID
   * @returns {Promise} 生成结果
   */
  async regenerateUserAuth(id) {
    return this.http.post(API_ENDPOINTS.USERS.REGENERATE_AUTH(id));
  }

  // ==================== 系统信息 API ====================

  /**
   * 获取系统信息
   * @returns {Promise} 系统信息
   */
  async getSystemInfo() {
    return this.http.get(API_ENDPOINTS.SYSTEM.INFO);
  }

  /**
   * 获取 API 文档
   * @returns {Promise} API 文档
   */
  async getApiDocs() {
    return this.http.get(API_ENDPOINTS.SYSTEM.DOCS);
  }

  // ==================== 辅助方法 ====================

  /**
   * 检查 API 连接状态
   * @returns {Promise} 连接状态
   */
  async checkConnection() {
    try {
      const result = await this.getSystemInfo();
      return {
        connected: result.success,
        message: result.success ? '连接正常' : '连接失败',
        data: result.data
      };
    } catch (error) {
      return {
        connected: false,
        message: '连接失败',
        error: error.message
      };
    }
  }

  /**
   * 设置 API 基础 URL
   * @param {string} baseUrl - 基础 URL
   */
  setBaseUrl(baseUrl) {
    this.http.setBaseUrl(baseUrl);
  }

  /**
   * 获取当前 API 配置
   * @returns {object} API 配置
   */
  getConfig() {
    return this.http.getConfig();
  }

  /**
   * 批量操作辅助方法
   * @param {Array} operations - 操作数组
   * @returns {Promise} 批量操作结果
   */
  async batchOperation(operations) {
    const results = [];
    
    for (const operation of operations) {
      try {
        const result = await operation();
        results.push({ success: true, data: result });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }

  /**
   * 处理 API 响应
   * @param {object} response - API 响应
   * @returns {object} 处理后的响应
   */
  handleResponse(response) {
    if (response.success) {
      return {
        success: true,
        data: response.data,
        message: '操作成功'
      };
    } else {
      return {
        success: false,
        error: response.error || '操作失败',
        message: response.error || '未知错误'
      };
    }
  }
}

// 创建单例实例
const apiService = new ApiService();

module.exports = apiService;
