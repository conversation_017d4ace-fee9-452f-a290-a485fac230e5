/**
 * 用户服务层
 * 处理用户相关的业务逻辑
 */

const User = require('../models/User');
const { validateUserData, validateObjectId, validatePagination, validateAuthCode } = require('../utils/validation');
const { createError } = require('../utils/response');
const { ERROR_CODES, USER_TYPES } = require('../constants');

/**
 * 用户服务类
 */
class UserService {
  /**
   * 用户认证
   * @param {string} authCode - 认证码
   * @param {string} ip - 用户IP
   * @param {string} userAgent - 用户代理
   * @returns {Promise<Object>} 认证结果
   */
  async authenticateUser(authCode, ip, userAgent) {
    // 验证认证码格式
    const authCodeValidation = validateAuthCode(authCode);
    if (!authCodeValidation.isValid) {
      throw createError(
        authCodeValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 查找用户
    const user = await User.findByAuthCode(authCodeValidation.value);
    if (!user) {
      throw createError(
        '无效的认证码',
        ERROR_CODES.INVALID_AUTH_CODE,
        401
      );
    }

    // 更新登录信息
    await user.updateLoginInfo(ip, userAgent);

    return {
      user: user.toSafeJSON(),
      message: '认证成功'
    };
  }

  /**
   * 获取用户列表
   * @param {Object} options - 查询选项
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} 用户列表和分页信息
   */
  async getUsers(options = {}, currentUser = null) {
    const {
      page = 1,
      limit = 10,
      userType,
      isActive,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options;

    // 验证分页参数
    const paginationValidation = validatePagination(page, limit);
    if (!paginationValidation.isValid) {
      throw createError(
        paginationValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 构建查询条件
    const query = {};

    // 用户类型过滤
    if (userType && Object.values(USER_TYPES).includes(userType)) {
      query.userType = userType;
    }

    // 活跃状态过滤
    if (isActive !== undefined) {
      query.isActive = isActive;
    }

    // 搜索过滤
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { username: searchRegex },
        { displayName: searchRegex },
        { email: searchRegex },
        { description: searchRegex }
      ];
    }

    // 计算分页
    const { page: validPage, limit: validLimit } = paginationValidation.value;
    const skip = (validPage - 1) * validLimit;

    // 构建排序
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // 执行查询
    const [users, total] = await Promise.all([
      User.find(query)
        .sort(sort)
        .skip(skip)
        .limit(validLimit)
        .lean(),
      User.countDocuments(query)
    ]);

    // 格式化用户数据（根据当前用户权限决定是否包含敏感信息）
    const safeUsers = users.map(user => {
      // 如果当前用户是管理员，返回包含认证码的完整数据
      if (currentUser && currentUser.userType === 'admin') {
        const { metadata, ...adminUser } = user;
        return {
          ...adminUser,
          // 添加虚拟字段
          formattedCreatedAt: user.createdAt ? new Date(user.createdAt).toLocaleDateString('zh-CN') : null,
          formattedUpdatedAt: user.updatedAt ? new Date(user.updatedAt).toLocaleDateString('zh-CN') : null,
          formattedLastLoginAt: user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString('zh-CN') : '从未登录',
          userTypeDisplay: user.userType === 'admin' ? '管理员' : '普通用户',
          isOnline: user.lastLoginAt ? (new Date(Date.now() - 5 * 60 * 1000) < new Date(user.lastLoginAt)) : false
        };
      } else {
        // 普通用户或未认证用户，移除敏感信息
        const { authCode, metadata, ...safeUser } = user;
        return {
          ...safeUser,
          // 添加虚拟字段
          formattedCreatedAt: user.createdAt ? new Date(user.createdAt).toLocaleDateString('zh-CN') : null,
          formattedUpdatedAt: user.updatedAt ? new Date(user.updatedAt).toLocaleDateString('zh-CN') : null,
          formattedLastLoginAt: user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString('zh-CN') : '从未登录',
          userTypeDisplay: user.userType === 'admin' ? '管理员' : '普通用户',
          isOnline: user.lastLoginAt ? (new Date(Date.now() - 5 * 60 * 1000) < new Date(user.lastLoginAt)) : false
        };
      }
    });

    return {
      users: safeUsers,
      pagination: {
        page: validPage,
        limit: validLimit,
        total,
        pages: Math.ceil(total / validLimit)
      }
    };
  }

  /**
   * 根据ID获取用户
   * @param {string} id - 用户ID
   * @param {boolean} includePrivate - 是否包含私有信息
   * @returns {Promise<Object>} 用户信息
   */
  async getUserById(id, includePrivate = false) {
    // 验证ID格式
    const idValidation = validateObjectId(id);
    if (!idValidation.isValid) {
      throw createError(
        idValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    const user = await User.findById(id).lean();
    if (!user) {
      throw createError(
        '用户未找到',
        ERROR_CODES.USER_NOT_FOUND,
        404
      );
    }

    // 根据权限返回不同的数据
    if (includePrivate) {
      return user;
    } else {
      const { authCode, metadata, ...safeUser } = user;
      return safeUser;
    }
  }

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @param {string} creatorId - 创建者ID
   * @returns {Promise<Object>} 创建的用户
   */
  async createUser(userData, creatorId) {
    // 验证用户数据
    const validation = validateUserData(userData);
    if (!validation.isValid) {
      throw createError(
        '数据验证失败',
        ERROR_CODES.VALIDATION_ERROR,
        400,
        validation.errors
      );
    }

    // 检查用户名是否已存在
    if (validation.data.username) {
      const existingUser = await User.findOne({ 
        username: validation.data.username 
      });

      if (existingUser) {
        throw createError(
          '用户名已存在',
          ERROR_CODES.DUPLICATE_KEY,
          409
        );
      }
    }

    // 检查邮箱是否已存在
    if (validation.data.email) {
      const existingEmail = await User.findOne({ 
        email: validation.data.email 
      });

      if (existingEmail) {
        throw createError(
          '邮箱已存在',
          ERROR_CODES.DUPLICATE_KEY,
          409
        );
      }
    }

    // 检查认证码是否已存在
    if (validation.data.authCode) {
      const existingAuthCode = await User.findOne({ 
        authCode: validation.data.authCode 
      });

      if (existingAuthCode) {
        throw createError(
          '认证码已存在',
          ERROR_CODES.DUPLICATE_KEY,
          409
        );
      }
    }

    // 创建用户
    const user = new User(validation.data);
    await user.save();

    return user.toSafeJSON();
  }

  /**
   * 更新用户
   * @param {string} id - 用户ID
   * @param {Object} updateData - 更新数据
   * @param {string} updaterId - 更新者ID
   * @returns {Promise<Object>} 更新后的用户
   */
  async updateUser(id, updateData, updaterId) {
    // 验证ID格式
    const idValidation = validateObjectId(id);
    if (!idValidation.isValid) {
      throw createError(
        idValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 验证更新数据
    const validation = validateUserData(updateData);
    if (!validation.isValid) {
      throw createError(
        '数据验证失败',
        ERROR_CODES.VALIDATION_ERROR,
        400,
        validation.errors
      );
    }

    // 检查用户是否存在
    const user = await User.findById(id);
    if (!user) {
      throw createError(
        '用户未找到',
        ERROR_CODES.USER_NOT_FOUND,
        404
      );
    }

    // 检查用户名冲突
    if (validation.data.username && validation.data.username !== user.username) {
      const existingUser = await User.findOne({
        username: validation.data.username,
        _id: { $ne: id }
      });

      if (existingUser) {
        throw createError(
          '用户名已存在',
          ERROR_CODES.DUPLICATE_KEY,
          409
        );
      }
    }

    // 检查邮箱冲突
    if (validation.data.email && validation.data.email !== user.email) {
      const existingEmail = await User.findOne({
        email: validation.data.email,
        _id: { $ne: id }
      });

      if (existingEmail) {
        throw createError(
          '邮箱已存在',
          ERROR_CODES.DUPLICATE_KEY,
          409
        );
      }
    }

    // 更新用户
    Object.assign(user, validation.data);
    await user.save();

    return user.toSafeJSON();
  }

  /**
   * 删除用户（软删除）
   * @param {string} id - 用户ID
   * @param {string} deleterId - 删除者ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteUser(id, deleterId) {
    // 验证ID格式
    const idValidation = validateObjectId(id);
    if (!idValidation.isValid) {
      throw createError(
        idValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 检查用户是否存在
    const user = await User.findById(id);
    if (!user) {
      throw createError(
        '用户未找到',
        ERROR_CODES.USER_NOT_FOUND,
        404
      );
    }

    // 防止删除自己
    if (id === deleterId) {
      throw createError(
        '不能删除自己',
        ERROR_CODES.FORBIDDEN,
        403
      );
    }

    // 软删除（停用用户）
    await user.deactivate();

    return { message: '用户已删除' };
  }

  /**
   * 重置用户认证码
   * @param {string} id - 用户ID
   * @param {string} operatorId - 操作者ID
   * @returns {Promise<Object>} 新的认证码
   */
  async resetAuthCode(id, operatorId) {
    // 验证ID格式
    const idValidation = validateObjectId(id);
    if (!idValidation.isValid) {
      throw createError(
        idValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 检查用户是否存在
    const user = await User.findById(id);
    if (!user) {
      throw createError(
        '用户未找到',
        ERROR_CODES.USER_NOT_FOUND,
        404
      );
    }

    // 重置认证码
    await user.resetAuthCode();

    return {
      message: '认证码已重置',
      authCode: user.authCode
    };
  }

  /**
   * 获取活跃用户
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 活跃用户列表
   */
  async getActiveUsers(options = {}) {
    const { limit, userType } = options;

    const queryOptions = { limit };
    if (userType) {
      queryOptions.userType = userType;
    }

    const users = await User.getActiveUsers(queryOptions).lean();
    
    // 移除敏感信息
    return users.map(user => {
      const { authCode, metadata, ...safeUser } = user;
      return safeUser;
    });
  }

  /**
   * 获取管理员用户
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 管理员用户列表
   */
  async getAdminUsers(options = {}) {
    const users = await User.getAdminUsers(options).lean();
    
    // 移除敏感信息
    return users.map(user => {
      const { authCode, metadata, ...safeUser } = user;
      return safeUser;
    });
  }

  /**
   * 搜索用户
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  async searchUsers(keyword, options = {}) {
    if (!keyword || keyword.trim().length === 0) {
      return [];
    }

    const users = await User.search(keyword.trim(), options).lean();
    
    // 移除敏感信息
    return users.map(user => {
      const { authCode, metadata, ...safeUser } = user;
      return safeUser;
    });
  }

  /**
   * 获取用户统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getUserStats() {
    const stats = await User.getStats();
    
    // 格式化统计结果
    const formattedStats = {
      total: 0,
      active: 0,
      inactive: 0,
      admins: 0,
      users: 0,
      totalLogins: 0
    };

    stats.forEach(stat => {
      const { userType, isActive } = stat._id;
      formattedStats.total += stat.count;
      formattedStats.totalLogins += stat.totalLogins || 0;
      
      if (isActive) {
        formattedStats.active += stat.count;
        if (userType === USER_TYPES.ADMIN) {
          formattedStats.admins += stat.count;
        } else {
          formattedStats.users += stat.count;
        }
      } else {
        formattedStats.inactive += stat.count;
      }
    });

    return formattedStats;
  }

  /**
   * 获取在线用户
   * @returns {Promise<Array>} 在线用户列表
   */
  async getOnlineUsers() {
    const users = await User.getOnlineUsers().lean();
    
    // 移除敏感信息
    return users.map(user => {
      const { authCode, metadata, ...safeUser } = user;
      return safeUser;
    });
  }

  /**
   * 批量更新用户状态
   * @param {Array} ids - 用户ID数组
   * @param {boolean} isActive - 新的活跃状态
   * @param {string} operatorId - 操作者ID
   * @returns {Promise<Object>} 更新结果
   */
  async batchUpdateStatus(ids, isActive, operatorId) {
    // 验证ID数组
    for (const id of ids) {
      const idValidation = validateObjectId(id);
      if (!idValidation.isValid) {
        throw createError(
          `无效的用户ID: ${id}`,
          ERROR_CODES.VALIDATION_ERROR,
          400
        );
      }
    }

    // 防止操作自己
    if (ids.includes(operatorId)) {
      throw createError(
        '不能修改自己的状态',
        ERROR_CODES.FORBIDDEN,
        403
      );
    }

    // 批量更新
    const result = await User.updateMany(
      { _id: { $in: ids } },
      { 
        isActive,
        updatedAt: new Date()
      }
    );

    return {
      message: `已更新 ${result.modifiedCount} 个用户的状态`,
      modifiedCount: result.modifiedCount
    };
  }
}

module.exports = new UserService();
