const { ipc<PERSON>enderer } = require('electron');

// 国际化文本
let i18nTexts = {};

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {
    // 加载国际化文本
    await loadI18nTexts();

    // 加载并应用主题
    await loadAndApplyTheme();
});

// 监听语言更改事件
ipcRenderer.on('language-changed', async (event, newLanguage) => {
    console.log('收到语言更改通知:', newLanguage);
    await loadI18nTexts();
});

// 监听主题更改事件
ipcRenderer.on('theme-changed', (event, theme) => {
    console.log('主界面收到主题更改通知:', theme);
    applyTheme(theme);
});

// 加载并应用主题
async function loadAndApplyTheme() {
    try {
        const theme = await ipcRenderer.invoke('get-setting', 'theme') || 'auto';
        applyTheme(theme);
    } catch (error) {
        console.error('加载主题失败:', error);
        applyTheme('auto');
    }
}

// 应用主题
function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    console.log('主界面主题已应用:', theme);
}

// 加载国际化文本
async function loadI18nTexts() {
    try {
        const language = await ipcRenderer.invoke('get-setting', 'language') || 'zh-CN';
        console.log('主界面加载语言:', language);

        i18nTexts = await ipcRenderer.invoke('get-i18n-texts', language);
        console.log('主界面获取到的i18nTexts:', Object.keys(i18nTexts).slice(0, 10));
        console.log('app.title文本:', i18nTexts['app.title']);

        updateI18nTexts();
    } catch (error) {
        console.error('加载国际化文本失败:', error);
    }
}

// 更新界面文本
function updateI18nTexts() {
    console.log('正在更新界面文本，当前i18nTexts:', Object.keys(i18nTexts).slice(0, 5));

    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        const text = getNestedValue(i18nTexts, key);

        if (text) {
            console.log(`更新文本: ${key} -> ${text}`);
            element.textContent = text;
        } else {
            console.warn(`未找到翻译文本: ${key}`);
        }
    });
}

// 获取嵌套对象的值
function getNestedValue(obj, key) {
    return key.split('.').reduce((current, keyPart) => {
        return current && current[keyPart] !== undefined ? current[keyPart] : undefined;
    }, obj);
}







// 打开管理面板
async function openAdminPanel() {
    const statusElement = document.getElementById('status');
    statusElement.textContent = getNestedValue(i18nTexts, 'main.status.opening_admin') || '正在打开管理面板...';

    try {
        const result = await ipcRenderer.invoke('open-admin-panel');
        if (result.success) {
            statusElement.textContent = result.message;
        } else {
            statusElement.textContent = result.error;
        }
    } catch (error) {
        console.error('打开管理面板失败:', error);
        statusElement.textContent = '打开管理面板失败: ' + error.message;
    }
}

// 打开设置页面
async function openSettings() {
    const statusElement = document.getElementById('status');
    statusElement.textContent = getNestedValue(i18nTexts, 'main.status.opening_settings') || '正在打开设置页面...';

    try {
        const result = await ipcRenderer.invoke('open-settings');
        if (result.success) {
            statusElement.textContent = getNestedValue(i18nTexts, 'main.status.settings_opened') || '设置页面已打开';
        } else {
            statusElement.textContent = result.error;
        }
    } catch (error) {
        console.error('打开设置页面失败:', error);
        statusElement.textContent = '打开设置页面失败: ' + error.message;
    }
}





// 打开开发者工具
function openDevTools() {
    // 这个功能需要在主进程中实现
    const statusElement = document.getElementById('status');
    statusElement.textContent = '开发者工具已在主进程中配置';
}

// 监听键盘快捷键
document.addEventListener('keydown', (event) => {
    // Ctrl+R 或 Cmd+R 刷新页面
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        location.reload();
    }
    
    // F12 打开开发者工具（在开发模式下）
    if (event.key === 'F12') {
        event.preventDefault();
        openDevTools();
    }
});

// 添加一些交互效果
document.querySelectorAll('button').forEach(button => {
    button.addEventListener('mouseenter', () => {
        button.style.transform = 'translateY(-2px)';
    });
    
    button.addEventListener('mouseleave', () => {
        button.style.transform = 'translateY(0)';
    });
});
