/**
 * 文件控制器
 * 处理文件上传、下载、删除等操作
 */

const { File, FILE_STATUS, FILE_TYPES } = require('../models/File');
const { deleteFile, getFileInfo } = require('../middleware/upload');
const { asyncHandler } = require('../utils/asyncHandler');
const { success, error, paginated } = require('../utils/response');
const { SUCCESS_MESSAGES, ERROR_MESSAGES } = require('../constants/messages');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

/**
 * 计算文件哈希值
 * @param {string} filePath - 文件路径
 * @returns {Promise<string>} - 文件的SHA-256哈希值
 */
function calculateFileHash(filePath) {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha256');
    const stream = fs.createReadStream(filePath);

    stream.on('data', (data) => {
      hash.update(data);
    });

    stream.on('end', () => {
      resolve(hash.digest('hex'));
    });

    stream.on('error', (err) => {
      reject(err);
    });
  });
}

/**
 * 文件控制器类
 */
class FileController {
  /**
   * 上传文件
   */
  uploadFile = asyncHandler(async (req, res) => {
    if (!req.file) {
      return error(res, '没有上传文件', 400);
    }

    const { description, tags, category, isPublic, relatedModel, relatedId, hash } = req.body;

    try {
      // 使用客户端传来的哈希值，如果没有则计算
      let fileHash = hash;
      if (!fileHash) {
        fileHash = await calculateFileHash(req.file.path);
      }

      // 检查是否已存在相同哈希的文件
      const existingFile = await File.findOne({
        hash: fileHash,
        status: FILE_STATUS.ACTIVE
      }).populate('uploadedBy', 'username displayName');

      if (existingFile) {
        // 删除刚上传的重复文件
        try {
          if (fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
          }
        } catch (unlinkError) {
          console.warn('删除重复文件失败:', unlinkError);
        }

        // 返回已存在的文件信息
        return success(res, {
          ...existingFile.toObject(),
          isDuplicate: true
        }, '文件已存在，已返回现有文件');
      }

      // 创建文件记录
      const fileData = {
        originalName: req.file.originalname,
        filename: req.file.filename,
        path: req.file.path,
        size: req.file.size,
        mimetype: req.file.mimetype,
        hash: fileHash, // 添加哈希值
        description: description || '',
        isPublic: isPublic === 'true',
        tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
        category: category || ''
      };

      // 如果有用户信息，设置上传者
      if (req.user?.id) {
        fileData.uploadedBy = req.user.id;
      }

      console.log('fileData before save:', fileData);

      // 添加处理后的元数据（如果有）
      if (req.file.processedMetadata) {
        fileData.metadata = {
          width: req.file.processedMetadata.width,
          height: req.file.processedMetadata.height,
          extension: path.extname(req.file.originalname).toLowerCase().slice(1)
        };
      }

      // 设置关联对象
      if (relatedModel && relatedId) {
        fileData.relatedTo = {
          model: relatedModel,
          objectId: relatedId
        };
      }

      const file = new File(fileData);
      await file.save();

      // 填充上传者信息
      await file.populate('uploadedBy', 'username displayName');

      return success(res, file, '文件上传成功');
    } catch (err) {
      console.error('保存文件记录失败:', err);
      
      // 删除已上传的文件
      if (req.file.path && fs.existsSync(req.file.path)) {
        await deleteFile(req.file.path);
      }
      
      return error(res, '文件上传失败: ' + err.message, 500);
    }
  });

  /**
   * 获取文件列表
   */
  getFiles = asyncHandler(async (req, res) => {
    const {
      page = 1,
      limit = 20,
      fileType,
      status = FILE_STATUS.ACTIVE,
      search,
      uploadedBy,
      tags,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 }
    };

    // 构建查询条件
    const query = { status };

    if (fileType) {
      query.fileType = fileType;
    }

    if (uploadedBy) {
      query.uploadedBy = uploadedBy;
    }

    if (tags) {
      query.tags = { $in: tags.split(',') };
    }

    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { originalName: searchRegex },
        { description: searchRegex },
        { tags: searchRegex }
      ];
    }

    // 权限控制：非管理员只能看到自己上传的文件或公开文件
    if (req.user.userType !== 'admin') {
      query.$or = [
        { uploadedBy: req.user.id },
        { isPublic: true }
      ];
    }

    const files = await File.find(query)
      .populate('uploadedBy', 'username displayName')
      .sort(options.sort)
      .limit(options.limit * 1)
      .skip((options.page - 1) * options.limit);

    const total = await File.countDocuments(query);

    const pagination = {
      page: options.page,
      limit: options.limit,
      total,
      pages: Math.ceil(total / options.limit)
    };

    return paginated(res, files, pagination, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 根据ID获取文件详情
   */
  getFileById = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const file = await File.findById(id).populate('uploadedBy', 'username displayName');

    if (!file) {
      return error(res, '文件不存在', 404);
    }

    // 权限检查
    if (file.status === FILE_STATUS.DELETED) {
      return error(res, '文件已被删除', 404);
    }

    if (!file.isPublic && file.uploadedBy._id.toString() !== req.user.id.toString() && req.user.userType !== 'admin') {
      return error(res, '没有权限访问此文件', 403);
    }

    return success(res, file, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 下载文件
   */
  downloadFile = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const file = await File.findById(id);

    if (!file || file.status === FILE_STATUS.DELETED) {
      return error(res, '文件不存在', 404);
    }

    // 权限检查
    if (!file.isPublic && file.uploadedBy.toString() !== req.user.id.toString() && req.user.userType !== 'admin') {
      return error(res, '没有权限下载此文件', 403);
    }

    // 检查文件是否存在
    if (!fs.existsSync(file.path)) {
      return error(res, '文件已丢失', 404);
    }

    // 增加下载次数
    await file.incrementDownload();

    // 设置响应头
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.originalName)}"`);
    res.setHeader('Content-Type', file.mimetype);

    // 发送文件
    res.sendFile(path.resolve(file.path));
  });

  /**
   * 删除文件
   */
  deleteFile = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const file = await File.findById(id);

    if (!file) {
      return error(res, '文件不存在', 404);
    }

    // 权限检查
    if (file.uploadedBy.toString() !== req.user.id.toString() && req.user.userType !== 'admin') {
      return error(res, '没有权限删除此文件', 403);
    }

    // 软删除
    await file.softDelete();

    // 可选：物理删除文件（根据业务需求决定）
    // if (fs.existsSync(file.path)) {
    //   await deleteFile(file.path);
    // }

    return success(res, null, '文件删除成功');
  });

  /**
   * 恢复文件
   */
  restoreFile = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const file = await File.findById(id);

    if (!file) {
      return error(res, '文件不存在', 404);
    }

    // 权限检查（仅管理员可恢复）
    if (req.user.userType !== 'admin') {
      return error(res, '没有权限恢复文件', 403);
    }

    await file.restore();

    return success(res, file, '文件恢复成功');
  });

  /**
   * 更新文件信息
   */
  updateFile = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { description, tags, category, isPublic } = req.body;

    const file = await File.findById(id);

    if (!file) {
      return error(res, '文件不存在', 404);
    }

    // 权限检查
    if (file.uploadedBy.toString() !== req.user.id.toString() && req.user.userType !== 'admin') {
      return error(res, '没有权限修改此文件', 403);
    }

    // 更新字段
    if (description !== undefined) file.description = description;
    if (tags !== undefined) file.tags = tags.split(',').map(tag => tag.trim());
    if (category !== undefined) file.category = category;
    if (isPublic !== undefined) file.isPublic = isPublic;

    await file.save();
    await file.populate('uploadedBy', 'username displayName');

    return success(res, file, '文件信息更新成功');
  });

  /**
   * 获取文件统计信息
   */
  getFileStats = asyncHandler(async (req, res) => {
    const stats = await File.getStats();
    
    const totalFiles = await File.countDocuments({ status: FILE_STATUS.ACTIVE });
    const totalSize = await File.aggregate([
      { $match: { status: FILE_STATUS.ACTIVE } },
      { $group: { _id: null, total: { $sum: '$size' } } }
    ]);

    const result = {
      totalFiles,
      totalSize: totalSize[0]?.total || 0,
      byType: stats
    };

    return success(res, result, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 批量删除文件
   */
  batchDeleteFiles = asyncHandler(async (req, res) => {
    const { fileIds } = req.body;

    if (!Array.isArray(fileIds) || fileIds.length === 0) {
      return error(res, '请提供要删除的文件ID列表', 400);
    }

    // 查找文件并检查权限
    const files = await File.find({ _id: { $in: fileIds } });
    
    for (const file of files) {
      if (file.uploadedBy.toString() !== req.user.id.toString() && req.user.userType !== 'admin') {
        return error(res, `没有权限删除文件: ${file.originalName}`, 403);
      }
    }

    // 批量软删除
    await File.updateMany(
      { _id: { $in: fileIds } },
      { status: FILE_STATUS.DELETED }
    );

    return success(res, { deletedCount: files.length }, '批量删除成功');
  });



  /**
   * 清理重复文件
   */
  cleanupDuplicateFiles = asyncHandler(async (req, res) => {
    try {
      // 查找所有有哈希值的文件，按哈希分组
      const duplicateGroups = await File.aggregate([
        {
          $match: {
            status: FILE_STATUS.ACTIVE,
            hash: { $exists: true, $ne: null, $ne: '' }
          }
        },
        {
          $group: {
            _id: '$hash',
            files: { $push: '$$ROOT' },
            count: { $sum: 1 }
          }
        },
        { $match: { count: { $gt: 1 } } } // 只保留有重复的组
      ]);

      let deletedCount = 0;
      let freedSpace = 0;

      for (const group of duplicateGroups) {
        const files = group.files;
        // 保留最早上传的文件，删除其他重复文件
        const filesToKeep = files.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))[0];
        const filesToDelete = files.filter(f => f._id.toString() !== filesToKeep._id.toString());

        for (const fileToDelete of filesToDelete) {
          try {
            // 删除物理文件
            if (fs.existsSync(fileToDelete.path)) {
              fs.unlinkSync(fileToDelete.path);
              freedSpace += fileToDelete.size;
            }

            // 删除数据库记录
            await File.findByIdAndUpdate(fileToDelete._id, {
              status: FILE_STATUS.DELETED,
              deletedAt: new Date()
            });

            deletedCount++;
          } catch (error) {
            console.error(`删除重复文件失败 ${fileToDelete._id}:`, error);
          }
        }
      }

      return success(res, {
        deletedCount,
        freedSpace,
        duplicateGroups: duplicateGroups.length
      }, `清理完成，删除了 ${deletedCount} 个重复文件，释放空间 ${(freedSpace / 1024 / 1024).toFixed(2)} MB`);

    } catch (error) {
      console.error('清理重复文件失败:', error);
      return error(res, '清理重复文件失败: ' + error.message, 500);
    }
  });
}

module.exports = new FileController();
