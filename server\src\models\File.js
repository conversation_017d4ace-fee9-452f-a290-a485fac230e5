/**
 * 文件模型
 * 用于管理上传的文件信息
 */

const mongoose = require('mongoose');

/**
 * 文件状态枚举
 */
const FILE_STATUS = {
  ACTIVE: 'active',      // 活跃状态
  DELETED: 'deleted',    // 已删除
  PROCESSING: 'processing' // 处理中
};

/**
 * 文件类型枚举
 */
const FILE_TYPES = {
  IMAGE: 'image',
  DOCUMENT: 'document',
  VIDEO: 'video',
  AUDIO: 'audio',
  OTHER: 'other'
};

/**
 * 文件模式定义
 */
const fileSchema = new mongoose.Schema({
  // 基本信息
  originalName: {
    type: String,
    required: [true, '原始文件名不能为空'],
    trim: true,
    maxlength: [255, '文件名不能超过255个字符']
  },
  
  filename: {
    type: String,
    required: [true, '文件名不能为空'],
    unique: true,
    trim: true,
    maxlength: [255, '文件名不能超过255个字符']
  },
  
  path: {
    type: String,
    required: [true, '文件路径不能为空'],
    trim: true
  },
  
  // 文件属性
  size: {
    type: Number,
    required: [true, '文件大小不能为空'],
    min: [0, '文件大小不能为负数']
  },
  
  mimetype: {
    type: String,
    required: [true, '文件类型不能为空'],
    trim: true
  },

  // 文件哈希值（用于去重）
  hash: {
    type: String,
    trim: true,
    index: true // 添加索引以提高查询性能
  },

  fileType: {
    type: String,
    enum: Object.values(FILE_TYPES),
    default: FILE_TYPES.OTHER
  },
  
  // 文件元数据
  metadata: {
    width: Number,        // 图片宽度
    height: Number,       // 图片高度
    duration: Number,     // 视频/音频时长
    encoding: String,     // 文件编码
    extension: String     // 文件扩展名
  },
  
  // 状态管理
  status: {
    type: String,
    enum: Object.values(FILE_STATUS),
    default: FILE_STATUS.ACTIVE
  },
  
  // 访问控制
  isPublic: {
    type: Boolean,
    default: false
  },
  
  // 使用统计
  downloadCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  lastAccessedAt: {
    type: Date,
    default: Date.now
  },
  
  // 关联信息
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  
  // 标签和分类
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, '标签长度不能超过50个字符']
  }],
  
  category: {
    type: String,
    trim: true,
    maxlength: [100, '分类名称不能超过100个字符']
  },
  
  // 描述信息
  description: {
    type: String,
    trim: true,
    maxlength: [1000, '描述不能超过1000个字符']
  },
  
  // 关联的业务对象
  relatedTo: {
    model: {
      type: String,
      enum: ['Brand', 'User', 'Other']
    },
    objectId: {
      type: mongoose.Schema.Types.ObjectId
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

/**
 * 索引定义
 */
fileSchema.index({ filename: 1 }, { unique: true });
fileSchema.index({ uploadedBy: 1, createdAt: -1 });
fileSchema.index({ status: 1, createdAt: -1 });
fileSchema.index({ fileType: 1, status: 1 });
fileSchema.index({ tags: 1 });
fileSchema.index({ 'relatedTo.model': 1, 'relatedTo.objectId': 1 });

/**
 * 虚拟字段
 */

// 格式化文件大小
fileSchema.virtual('formattedSize').get(function() {
  const bytes = this.size;
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// 格式化创建时间
fileSchema.virtual('formattedCreatedAt').get(function() {
  return this.createdAt ? this.createdAt.toLocaleDateString('zh-CN') : null;
});

// 格式化更新时间
fileSchema.virtual('formattedUpdatedAt').get(function() {
  return this.updatedAt ? this.updatedAt.toLocaleDateString('zh-CN') : null;
});

// 文件URL（用于访问）
fileSchema.virtual('url').get(function() {
  return `/uploads/${this.filename}`;
});

// 是否为图片
fileSchema.virtual('isImage').get(function() {
  return this.fileType === FILE_TYPES.IMAGE;
});

/**
 * 实例方法
 */

// 增加下载次数
fileSchema.methods.incrementDownload = function() {
  this.downloadCount += 1;
  this.lastAccessedAt = new Date();
  return this.save();
};

// 软删除
fileSchema.methods.softDelete = function() {
  this.status = FILE_STATUS.DELETED;
  return this.save();
};

// 恢复文件
fileSchema.methods.restore = function() {
  this.status = FILE_STATUS.ACTIVE;
  return this.save();
};

// 添加标签
fileSchema.methods.addTag = function(tag) {
  if (!this.tags.includes(tag)) {
    this.tags.push(tag);
    return this.save();
  }
  return Promise.resolve(this);
};

// 移除标签
fileSchema.methods.removeTag = function(tag) {
  this.tags = this.tags.filter(t => t !== tag);
  return this.save();
};

/**
 * 静态方法
 */

// 根据文件类型获取文件
fileSchema.statics.getByType = function(fileType, options = {}) {
  const { limit, skip, status = FILE_STATUS.ACTIVE } = options;
  
  let query = this.find({ fileType, status });
  
  if (skip) query = query.skip(skip);
  if (limit) query = query.limit(limit);
  
  return query.sort({ createdAt: -1 }).populate('uploadedBy', 'username displayName');
};

// 根据上传者获取文件
fileSchema.statics.getByUploader = function(uploaderId, options = {}) {
  const { limit, skip, status = FILE_STATUS.ACTIVE } = options;
  
  let query = this.find({ uploadedBy: uploaderId, status });
  
  if (skip) query = query.skip(skip);
  if (limit) query = query.limit(limit);
  
  return query.sort({ createdAt: -1 });
};

// 搜索文件
fileSchema.statics.search = function(keyword, options = {}) {
  const { fileType, status = FILE_STATUS.ACTIVE, limit, skip } = options;
  
  const searchRegex = new RegExp(keyword, 'i');
  const query = {
    status,
    $or: [
      { originalName: searchRegex },
      { description: searchRegex },
      { tags: searchRegex }
    ]
  };
  
  if (fileType) {
    query.fileType = fileType;
  }
  
  let mongoQuery = this.find(query);
  
  if (skip) mongoQuery = mongoQuery.skip(skip);
  if (limit) mongoQuery = mongoQuery.limit(limit);
  
  return mongoQuery.sort({ createdAt: -1 }).populate('uploadedBy', 'username displayName');
};

// 获取文件统计信息
fileSchema.statics.getStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$fileType',
        count: { $sum: 1 },
        totalSize: { $sum: '$size' },
        avgSize: { $avg: '$size' }
      }
    }
  ]);
};

/**
 * 中间件
 */

// 保存前处理
fileSchema.pre('save', function(next) {
  console.log('=== File pre save 中间件 ===');
  console.log('this.fileType:', this.fileType);
  console.log('this.mimetype:', this.mimetype);
  console.log('this.uploadedBy:', this.uploadedBy);

  // 自动设置文件类型
  if (!this.fileType && this.mimetype) {
    if (this.mimetype.startsWith('image/')) {
      this.fileType = FILE_TYPES.IMAGE;
    } else if (this.mimetype.startsWith('video/')) {
      this.fileType = FILE_TYPES.VIDEO;
    } else if (this.mimetype.startsWith('audio/')) {
      this.fileType = FILE_TYPES.AUDIO;
    } else if (this.mimetype.includes('pdf') || this.mimetype.includes('document')) {
      this.fileType = FILE_TYPES.DOCUMENT;
    } else {
      this.fileType = FILE_TYPES.OTHER;
    }
    console.log('设置文件类型为:', this.fileType);
  }
  
  // 设置文件扩展名
  if (!this.metadata.extension && this.originalName) {
    const ext = this.originalName.split('.').pop();
    if (ext && ext !== this.originalName) {
      this.metadata.extension = ext.toLowerCase();
    }
  }
  
  next();
});

/**
 * 导出常量和模型
 */
module.exports = {
  File: mongoose.model('File', fileSchema),
  FILE_STATUS,
  FILE_TYPES
};
