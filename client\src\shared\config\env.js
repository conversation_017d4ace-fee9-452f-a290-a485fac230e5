/**
 * 环境变量配置模块
 * 统一管理环境变量的获取和默认值
 */

/**
 * 从环境变量获取配置，如果没有则使用默认值
 * @param {string} key - 环境变量键名
 * @param {any} defaultValue - 默认值
 * @param {string} type - 数据类型 ('string' | 'number' | 'boolean' | 'array')
 * @returns {any} 配置值
 */
function getEnvVar(key, defaultValue, type = 'string') {
  const value = process.env[key];
  
  if (value === undefined || value === null) {
    return defaultValue;
  }
  
  switch (type) {
    case 'number':
      const num = parseInt(value, 10);
      return isNaN(num) ? defaultValue : num;
      
    case 'boolean':
      return value.toLowerCase() === 'true';
      
    case 'array':
      return value.split(',').map(item => item.trim()).filter(item => item);
      
    case 'string':
    default:
      return value;
  }
}

/**
 * 应用配置
 */
const APP_CONFIG = {
  // 基础配置
  NODE_ENV: getEnvVar('NODE_ENV', 'development'),
  APP_VERSION: getEnvVar('APP_VERSION', '1.0.4'),
  APP_NAME: getEnvVar('APP_NAME', 'AI重器'),

  // API 配置
  API_BASE_URL_DEV: getEnvVar('API_BASE_URL_DEV', 'http://localhost:3000'),
  API_BASE_URL_TEST: getEnvVar('API_BASE_URL_TEST', 'http://test-api.aitools.com'),
  API_BASE_URL_PROD: getEnvVar('API_BASE_URL_PROD', 'https://api.aitools.com'),
  API_TIMEOUT: getEnvVar('API_TIMEOUT', 10000, 'number'),
  API_RETRY_TIMES: getEnvVar('API_RETRY_TIMES', 3, 'number'),

  // 认证配置
  DEFAULT_ADMIN_AUTH_CODE: getEnvVar('DEFAULT_ADMIN_AUTH_CODE', 'ADMIN2024'),
  DEFAULT_USER_AUTH_CODE: getEnvVar('DEFAULT_USER_AUTH_CODE', 'USER2024'),

  // 界面配置
  DEFAULT_LANGUAGE: getEnvVar('DEFAULT_LANGUAGE', 'zh-CN'),
  DEFAULT_THEME: getEnvVar('DEFAULT_THEME', 'auto'),
  DEV_TOOLS_ENABLED: getEnvVar('DEV_TOOLS_ENABLED', true, 'boolean'),

  // 窗口配置
  MAIN_WINDOW_WIDTH: getEnvVar('MAIN_WINDOW_WIDTH', 1200, 'number'),
  MAIN_WINDOW_HEIGHT: getEnvVar('MAIN_WINDOW_HEIGHT', 800, 'number'),
  LOGIN_WINDOW_WIDTH: getEnvVar('LOGIN_WINDOW_WIDTH', 500, 'number'),
  LOGIN_WINDOW_HEIGHT: getEnvVar('LOGIN_WINDOW_HEIGHT', 600, 'number'),
  SETTINGS_WINDOW_WIDTH: getEnvVar('SETTINGS_WINDOW_WIDTH', 1000, 'number'),
  SETTINGS_WINDOW_HEIGHT: getEnvVar('SETTINGS_WINDOW_HEIGHT', 800, 'number'),
  ADMIN_WINDOW_WIDTH: getEnvVar('ADMIN_WINDOW_WIDTH', 1400, 'number'),
  ADMIN_WINDOW_HEIGHT: getEnvVar('ADMIN_WINDOW_HEIGHT', 900, 'number'),

  // 开发调试配置
  DEBUG_MODE: getEnvVar('DEBUG_MODE', true, 'boolean')
};

/**
 * 获取当前环境
 * @returns {string} 当前环境
 */
function getCurrentEnvironment() {
  return APP_CONFIG.NODE_ENV;
}

/**
 * 检查是否为开发环境
 * @returns {boolean} 是否为开发环境
 */
function isDevelopment() {
  return getCurrentEnvironment() === 'development';
}

/**
 * 检查是否为生产环境
 * @returns {boolean} 是否为生产环境
 */
function isProduction() {
  return getCurrentEnvironment() === 'production';
}

/**
 * 检查是否为测试环境
 * @returns {boolean} 是否为测试环境
 */
function isTest() {
  return getCurrentEnvironment() === 'test';
}

/**
 * 获取 API 基础 URL（根据当前环境）
 * @returns {string} API 基础 URL
 */
function getApiBaseUrl() {
  const env = getCurrentEnvironment();
  
  switch (env) {
    case 'production':
      return APP_CONFIG.API_BASE_URL_PROD;
    case 'test':
      return APP_CONFIG.API_BASE_URL_TEST;
    case 'development':
    default:
      return APP_CONFIG.API_BASE_URL_DEV;
  }
}

/**
 * 获取窗口配置
 * @param {string} windowType - 窗口类型 ('main' | 'login' | 'settings' | 'admin')
 * @returns {object} 窗口配置
 */
function getWindowConfig(windowType) {
  const configs = {
    main: {
      width: APP_CONFIG.MAIN_WINDOW_WIDTH,
      height: APP_CONFIG.MAIN_WINDOW_HEIGHT
    },
    login: {
      width: APP_CONFIG.LOGIN_WINDOW_WIDTH,
      height: APP_CONFIG.LOGIN_WINDOW_HEIGHT
    },
    settings: {
      width: APP_CONFIG.SETTINGS_WINDOW_WIDTH,
      height: APP_CONFIG.SETTINGS_WINDOW_HEIGHT
    },
    admin: {
      width: APP_CONFIG.ADMIN_WINDOW_WIDTH,
      height: APP_CONFIG.ADMIN_WINDOW_HEIGHT
    }
  };
  
  return configs[windowType] || configs.main;
}

/**
 * 打印环境配置信息（仅开发环境）
 */
function printEnvConfig() {
  if (isDevelopment()) {
    console.log('\n📋 环境变量配置:');
    console.log(`   环境: ${getCurrentEnvironment()}`);
    console.log(`   应用名称: ${APP_CONFIG.APP_NAME}`);
    console.log(`   应用版本: ${APP_CONFIG.APP_VERSION}`);
    console.log(`   API 地址: ${getApiBaseUrl()}`);
    console.log(`   默认语言: ${APP_CONFIG.DEFAULT_LANGUAGE}`);
    console.log(`   默认主题: ${APP_CONFIG.DEFAULT_THEME}`);
    console.log(`   调试模式: ${APP_CONFIG.DEBUG_MODE}`);
    console.log('');
  }
}

module.exports = {
  APP_CONFIG,
  getEnvVar,
  getCurrentEnvironment,
  isDevelopment,
  isProduction,
  isTest,
  getApiBaseUrl,
  getWindowConfig,
  printEnvConfig
};
