/**
 * 文件上传组件
 * 支持拖拽上传、文件预览、进度显示等功能
 */

class FileUpload {
    constructor(options = {}) {
        this.options = {
            container: null,
            accept: 'image/*',
            maxSize: 10 * 1024 * 1024, // 10MB
            multiple: false,
            autoUpload: true,
            uploadUrl: null, // 将在init中动态设置
            onSuccess: null,
            onError: null,
            onProgress: null,
            ...options
        };

        this.files = [];
        this.isUploading = false;

        this.init();
    }

    async init() {
        if (!this.options.container) {
            throw new Error('容器元素是必需的');
        }

        // 如果没有设置uploadUrl，从API配置获取
        if (!this.options.uploadUrl) {
            try {
                await this.initUploadUrl();
            } catch (error) {
                // 显示错误信息给用户
                this.showError(`文件上传功能初始化失败: ${error.message}`);
                return;
            }
        }

        this.createUploadArea();
        this.bindEvents();
    }

    showError(message) {
        if (this.options.container) {
            this.options.container.innerHTML = `
                <div class="file-upload-error" style="
                    padding: 20px;
                    border: 2px dashed #dc3545;
                    border-radius: 8px;
                    text-align: center;
                    color: #dc3545;
                    background-color: #f8d7da;
                ">
                    <i class="bi bi-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <div style="font-weight: bold; margin-bottom: 5px;">文件上传功能不可用</div>
                    <div style="font-size: 0.9rem;">${message}</div>
                    <div style="font-size: 0.8rem; margin-top: 10px; color: #6c757d;">
                        请检查网络连接和API服务器状态
                    </div>
                </div>
            `;
        }
    }

    async initUploadUrl() {
        try {
            if (window.ipcRenderer) {
                const config = await window.ipcRenderer.invoke('get-api-config');
                if (config && config.BASE_URL) {
                    this.options.uploadUrl = `${config.BASE_URL}/api/files/upload-image`;
                } else {
                    throw new Error('API配置无效');
                }
            } else {
                throw new Error('IPC渲染器不可用');
            }
        } catch (error) {
            console.error('获取API配置失败:', error);
            // 抛出错误，让调用者知道初始化失败
            throw new Error(`文件上传初始化失败: ${error.message}`);
        }
    }

    createUploadArea() {
        const container = this.options.container;
        
        container.innerHTML = `
            <div class="file-upload-area" id="fileUploadArea">
                <div class="upload-content">
                    <div class="upload-icon">
                        <i class="bi bi-cloud-upload"></i>
                    </div>
                    <div class="upload-text">
                        <p class="upload-title">拖拽文件到此处或点击上传</p>
                        <p class="upload-subtitle">支持 JPG、PNG、GIF、SVG 格式，最大 10MB</p>
                    </div>
                    <button type="button" class="btn btn-primary upload-btn">
                        <i class="bi bi-plus-lg me-1"></i>选择文件
                    </button>
                </div>
                <input type="file" id="fileInput" style="display: none;" 
                       accept="${this.options.accept}" 
                       ${this.options.multiple ? 'multiple' : ''}>
                
                <!-- 文件预览区域 -->
                <div class="file-preview-area" id="filePreviewArea" style="display: none;">
                    <div class="preview-header">
                        <span class="preview-title">文件预览</span>
                        <button type="button" class="btn btn-sm btn-outline-secondary clear-btn">
                            <i class="bi bi-x-lg"></i>清除
                        </button>
                    </div>
                    <div class="preview-content" id="previewContent"></div>
                </div>

                <!-- 上传进度 -->
                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">
                        <span class="progress-status">正在上传...</span>
                        <span class="progress-percent">0%</span>
                    </div>
                </div>
            </div>
        `;

        this.addStyles();
    }

    addStyles() {
        if (document.getElementById('fileUploadStyles')) return;

        const style = document.createElement('style');
        style.id = 'fileUploadStyles';
        style.textContent = `
            .file-upload-area {
                border: 2px dashed #dee2e6;
                border-radius: 8px;
                padding: 2rem;
                text-align: center;
                transition: all 0.3s ease;
                background-color: #f8f9fa;
            }

            .file-upload-area.drag-over {
                border-color: #0d6efd;
                background-color: #e7f3ff;
            }

            .upload-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 1rem;
            }

            .upload-icon {
                font-size: 3rem;
                color: #6c757d;
            }

            .upload-text {
                margin: 0;
            }

            .upload-title {
                font-size: 1.1rem;
                font-weight: 500;
                margin-bottom: 0.5rem;
                color: #495057;
            }

            .upload-subtitle {
                font-size: 0.9rem;
                color: #6c757d;
                margin-bottom: 0;
            }

            .file-preview-area {
                margin-top: 1rem;
                border-top: 1px solid #dee2e6;
                padding-top: 1rem;
            }

            .preview-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
            }

            .preview-title {
                font-weight: 500;
                color: #495057;
            }

            .preview-content {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 1rem;
            }

            .preview-item {
                position: relative;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                overflow: hidden;
                background: white;
            }

            .preview-image {
                width: 100%;
                height: 120px;
                object-fit: cover;
                display: block;
            }

            .preview-info {
                padding: 0.5rem;
                font-size: 0.8rem;
                color: #6c757d;
                text-align: center;
            }

            .preview-remove {
                position: absolute;
                top: 0.25rem;
                right: 0.25rem;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background: rgba(220, 53, 69, 0.8);
                color: white;
                border: none;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.7rem;
                cursor: pointer;
                opacity: 0;
                transition: opacity 0.2s;
            }

            .preview-item:hover .preview-remove {
                opacity: 1;
            }

            .upload-progress {
                margin-top: 1rem;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: 8px;
            }

            .progress-text {
                display: flex;
                justify-content: space-between;
                margin-top: 0.5rem;
                font-size: 0.9rem;
                color: #6c757d;
            }

            .file-upload-area.uploading {
                pointer-events: none;
                opacity: 0.7;
            }
        `;
        document.head.appendChild(style);
    }

    bindEvents() {
        const uploadArea = this.options.container.querySelector('#fileUploadArea');
        const fileInput = this.options.container.querySelector('#fileInput');
        const uploadBtn = this.options.container.querySelector('.upload-btn');
        const clearBtn = this.options.container.querySelector('.clear-btn');

        // 点击上传按钮
        uploadBtn.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            this.handleFiles(e.dataTransfer.files);
        });

        // 清除文件
        clearBtn.addEventListener('click', () => {
            this.clearFiles();
        });
    }

    handleFiles(fileList) {
        const files = Array.from(fileList);
        
        // 验证文件
        const validFiles = files.filter(file => this.validateFile(file));
        
        if (validFiles.length === 0) return;

        if (!this.options.multiple) {
            this.files = [validFiles[0]];
        } else {
            this.files.push(...validFiles);
        }

        this.showPreview();

        if (this.options.autoUpload) {
            this.uploadFiles();
        }
    }

    validateFile(file) {
        // 检查文件类型
        if (this.options.accept !== '*' && !this.isFileTypeAllowed(file)) {
            this.showError(`不支持的文件类型: ${file.type}`);
            return false;
        }

        // 检查文件大小
        if (file.size > this.options.maxSize) {
            this.showError(`文件大小超过限制: ${this.formatFileSize(this.options.maxSize)}`);
            return false;
        }

        return true;
    }

    isFileTypeAllowed(file) {
        const accept = this.options.accept;
        if (accept === '*') return true;

        const acceptTypes = accept.split(',').map(type => type.trim());
        
        return acceptTypes.some(acceptType => {
            if (acceptType.endsWith('/*')) {
                const category = acceptType.split('/')[0];
                return file.type.startsWith(category + '/');
            }
            return file.type === acceptType;
        });
    }

    showPreview() {
        const previewArea = this.options.container.querySelector('#filePreviewArea');
        const previewContent = this.options.container.querySelector('#previewContent');
        
        if (this.files.length === 0) {
            previewArea.style.display = 'none';
            return;
        }

        previewArea.style.display = 'block';
        previewContent.innerHTML = '';

        this.files.forEach((file, index) => {
            const previewItem = this.createPreviewItem(file, index);
            previewContent.appendChild(previewItem);
        });
    }

    createPreviewItem(file, index) {
        const item = document.createElement('div');
        item.className = 'preview-item';

        if (file.type.startsWith('image/')) {
            const img = document.createElement('img');
            img.className = 'preview-image';
            img.src = URL.createObjectURL(file);
            item.appendChild(img);
        }

        const info = document.createElement('div');
        info.className = 'preview-info';
        info.textContent = `${file.name} (${this.formatFileSize(file.size)})`;
        item.appendChild(info);

        const removeBtn = document.createElement('button');
        removeBtn.className = 'preview-remove';
        removeBtn.innerHTML = '<i class="bi bi-x"></i>';
        removeBtn.addEventListener('click', () => {
            this.removeFile(index);
        });
        item.appendChild(removeBtn);

        return item;
    }

    removeFile(index) {
        this.files.splice(index, 1);
        this.showPreview();
    }

    clearFiles() {
        this.files = [];
        this.showPreview();
        this.hideProgress();
    }

    async uploadFiles() {
        if (this.files.length === 0 || this.isUploading) return;

        this.isUploading = true;
        this.showProgress();

        try {
            const results = [];

            for (let i = 0; i < this.files.length; i++) {
                const file = this.files[i];

                // 更新进度显示
                this.updateProgress(0, `正在上传 ${file.name}...`);

                // 计算文件哈希并上传
                const fileHash = await this.calculateFileHash(file);
                const result = await this.uploadSingleFile(file, i, fileHash);

                results.push(result);
            }

            this.hideProgress();
            this.isUploading = false;

            if (this.options.onSuccess) {
                this.options.onSuccess(this.options.multiple ? results : results[0]);
            }

        } catch (error) {
            this.hideProgress();
            this.isUploading = false;
            this.showError(error.message);

            if (this.options.onError) {
                this.options.onError(error);
            }
        }
    }

    // 计算文件哈希值 (使用SHA-256，因为Web Crypto API不支持MD5)
    async calculateFileHash(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async function(e) {
                try {
                    const arrayBuffer = e.target.result;
                    const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
                    const hashArray = Array.from(new Uint8Array(hashBuffer));
                    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                    resolve(hashHex);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('读取文件失败'));
            reader.readAsArrayBuffer(file);
        });
    }



    async uploadSingleFile(file, index, fileHash = null) {
        const formData = new FormData();
        formData.append('image', file);

        // 添加文件哈希值
        if (fileHash) {
            formData.append('hash', fileHash);
        }

        // 添加额外的字段
        if (this.options.description) {
            formData.append('description', this.options.description);
        }
        if (this.options.category) {
            formData.append('category', this.options.category);
        }
        if (this.options.isPublic !== undefined) {
            formData.append('isPublic', this.options.isPublic);
        }
        if (this.options.relatedModel) {
            formData.append('relatedModel', this.options.relatedModel);
        }
        if (this.options.relatedId) {
            formData.append('relatedId', this.options.relatedId);
        }

        const { ipcRenderer } = require('electron');
        const currentUser = await ipcRenderer.invoke('get-current-user');



        const response = await fetch(this.options.uploadUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${currentUser.authCode}`
            },
            body: formData
        });



        if (!response.ok) {
            const errorText = await response.text();
            console.error('上传失败响应:', errorText);

            try {
                const error = JSON.parse(errorText);
                throw new Error(error.message || '上传失败');
            } catch (parseError) {
                throw new Error(`上传失败 (${response.status}): ${errorText}`);
            }
        }

        const result = await response.json();

        // 更新进度到100%
        this.updateProgress(100, '上传完成');

        // 检查是否为重复文件
        if (result.data && result.data.isDuplicate) {
            console.log('检测到重复文件:', result.data.message);
            // 可以在这里显示重复文件的提示
            // this.showInfo(result.message || '检测到重复文件，已返回现有文件');
        }

        return result.data;
    }

    showProgress() {
        const uploadArea = this.options.container.querySelector('#fileUploadArea');
        const progressArea = this.options.container.querySelector('#uploadProgress');

        uploadArea.classList.add('uploading');
        progressArea.style.display = 'block';
    }

    hideProgress() {
        const uploadArea = this.options.container.querySelector('#fileUploadArea');
        const progressArea = this.options.container.querySelector('#uploadProgress');

        uploadArea.classList.remove('uploading');
        progressArea.style.display = 'none';
    }

    updateProgress(percent, status) {
        const progressBar = this.options.container.querySelector('.progress-bar');
        const progressStatus = this.options.container.querySelector('.progress-status');
        const progressPercent = this.options.container.querySelector('.progress-percent');

        if (progressBar) {
            progressBar.style.width = `${percent}%`;
        }

        if (progressStatus && status) {
            progressStatus.textContent = status;
        }

        if (progressPercent) {
            progressPercent.textContent = `${Math.round(percent)}%`;
        }

        // 调用外部进度回调
        if (this.options.onProgress) {
            this.options.onProgress(percent, status);
        }
    }

    showError(message) {
        // 这里可以集成到现有的错误显示系统
        console.error('文件上传错误:', message);
        alert(message); // 临时使用alert，后续可以改为更好的UI
    }

    showInfo(message) {
        // 显示信息提示
        console.info('文件上传信息:', message);
        // 可以使用更友好的提示方式，这里暂时使用alert
        alert(message);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 公共方法
    getFiles() {
        return this.files;
    }

    setFiles(files) {
        this.files = files;
        this.showPreview();
    }

    upload() {
        return this.uploadFiles();
    }

    clear() {
        this.clearFiles();
    }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FileUpload;
} else {
    window.FileUpload = FileUpload;
}
