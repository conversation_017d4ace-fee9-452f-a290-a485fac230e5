/**
 * 用户控制器
 * 处理用户相关的HTTP请求
 */

const userService = require('../services/userService');
const { success, created, paginated, notFound, unauthorized, asyncHandler } = require('../utils/response');
const { SUCCESS_MESSAGES } = require('../constants');

/**
 * 用户控制器类
 */
class UserController {
  /**
   * 用户认证
   */
  authenticate = asyncHandler(async (req, res) => {
    const { authCode } = req.body;
    const ip = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');
    
    const result = await userService.authenticateUser(authCode, ip, userAgent);
    
    return success(res, result, '认证成功');
  });

  /**
   * 获取用户列表
   */
  getUsers = asyncHandler(async (req, res) => {
    const options = {
      page: req.query.page,
      limit: req.query.limit,
      userType: req.query.userType,
      isActive: req.query.isActive,
      search: req.query.search,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    // 传递当前用户信息，以便服务层决定返回数据的详细程度
    const result = await userService.getUsers(options, req.user);

    return paginated(
      res,
      result.users,
      result.pagination,
      SUCCESS_MESSAGES.RETRIEVED
    );
  });

  /**
   * 根据ID获取用户
   */
  getUserById = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const includePrivate = req.user?.isAdmin || req.user?.id === id;
    
    const user = await userService.getUserById(id, includePrivate);
    
    return success(res, user, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 创建用户
   */
  createUser = asyncHandler(async (req, res) => {
    const userData = req.body;
    const creatorId = req.user?.id;
    
    const user = await userService.createUser(userData, creatorId);
    
    return created(res, user, SUCCESS_MESSAGES.CREATED);
  });

  /**
   * 更新用户
   */
  updateUser = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const updaterId = req.user?.id;
    
    // 检查权限：只能更新自己或管理员可以更新任何人
    if (id !== updaterId && !req.user?.isAdmin) {
      return unauthorized(res, '权限不足');
    }
    
    const user = await userService.updateUser(id, updateData, updaterId);
    
    return success(res, user, SUCCESS_MESSAGES.UPDATED);
  });

  /**
   * 删除用户
   */
  deleteUser = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const deleterId = req.user?.id;
    
    const result = await userService.deleteUser(id, deleterId);
    
    return success(res, result, SUCCESS_MESSAGES.DELETED);
  });

  /**
   * 重置用户认证码
   */
  resetAuthCode = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const operatorId = req.user?.id;
    
    const result = await userService.resetAuthCode(id, operatorId);
    
    return success(res, result, '认证码已重置');
  });

  /**
   * 获取活跃用户
   */
  getActiveUsers = asyncHandler(async (req, res) => {
    const options = {
      limit: req.query.limit,
      userType: req.query.userType
    };

    const users = await userService.getActiveUsers(options);
    
    return success(res, users, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 获取管理员用户
   */
  getAdminUsers = asyncHandler(async (req, res) => {
    const options = {
      limit: req.query.limit,
      skip: req.query.skip
    };

    const users = await userService.getAdminUsers(options);
    
    return success(res, users, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 搜索用户
   */
  searchUsers = asyncHandler(async (req, res) => {
    const { keyword } = req.query;
    const options = {
      userType: req.query.userType,
      isActive: req.query.isActive,
      limit: req.query.limit,
      skip: req.query.skip
    };

    const users = await userService.searchUsers(keyword, options);
    
    return success(res, users, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 获取用户统计信息
   */
  getUserStats = asyncHandler(async (req, res) => {
    const stats = await userService.getUserStats();
    
    return success(res, stats, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 获取在线用户
   */
  getOnlineUsers = asyncHandler(async (req, res) => {
    const users = await userService.getOnlineUsers();
    
    return success(res, users, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 批量更新用户状态
   */
  batchUpdateStatus = asyncHandler(async (req, res) => {
    const { ids, isActive } = req.body;
    const operatorId = req.user?.id;
    
    const result = await userService.batchUpdateStatus(ids, isActive, operatorId);
    
    return success(res, result, SUCCESS_MESSAGES.UPDATED);
  });

  /**
   * 获取当前用户信息
   */
  getCurrentUser = asyncHandler(async (req, res) => {
    const userId = req.user?.id;
    
    if (!userId) {
      return unauthorized(res, '未登录');
    }
    
    const user = await userService.getUserById(userId, true);
    
    return success(res, user, SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 更新当前用户信息
   */
  updateCurrentUser = asyncHandler(async (req, res) => {
    const userId = req.user?.id;
    const updateData = req.body;
    
    if (!userId) {
      return unauthorized(res, '未登录');
    }
    
    // 移除敏感字段，防止用户修改权限等
    const { userType, isActive, authCode, ...safeUpdateData } = updateData;
    
    const user = await userService.updateUser(userId, safeUpdateData, userId);
    
    return success(res, user, SUCCESS_MESSAGES.UPDATED);
  });

  /**
   * 更新用户偏好设置
   */
  updateUserPreferences = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { preferences } = req.body;
    
    // 检查权限：只能更新自己的偏好设置
    if (id !== req.user?.id && !req.user?.isAdmin) {
      return unauthorized(res, '权限不足');
    }
    
    const User = require('../models/User');
    const user = await User.findById(id);
    
    if (!user) {
      return notFound(res, '用户未找到');
    }
    
    await user.updatePreferences(preferences);
    
    return success(res, user.toSafeJSON(), '偏好设置已更新');
  });

  /**
   * 激活用户
   */
  activateUser = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const User = require('../models/User');
    const user = await User.findById(id);
    
    if (!user) {
      return notFound(res, '用户未找到');
    }
    
    await user.activate();
    
    return success(res, user.toSafeJSON(), '用户已激活');
  });

  /**
   * 停用用户
   */
  deactivateUser = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const operatorId = req.user?.id;
    
    // 防止停用自己
    if (id === operatorId) {
      return unauthorized(res, '不能停用自己');
    }
    
    const User = require('../models/User');
    const user = await User.findById(id);
    
    if (!user) {
      return notFound(res, '用户未找到');
    }
    
    await user.deactivate();
    
    return success(res, user.toSafeJSON(), '用户已停用');
  });

  /**
   * 获取用户登录历史
   */
  getUserLoginHistory = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    // 检查权限：只能查看自己的登录历史或管理员可以查看任何人的
    if (id !== req.user?.id && !req.user?.isAdmin) {
      return unauthorized(res, '权限不足');
    }
    
    const User = require('../models/User');
    const user = await User.findById(id).select('metadata.loginHistory').lean();
    
    if (!user) {
      return notFound(res, '用户未找到');
    }
    
    return success(res, user.metadata?.loginHistory || [], SUCCESS_MESSAGES.RETRIEVED);
  });

  /**
   * 清除用户登录历史
   */
  clearUserLoginHistory = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    // 检查权限：只能清除自己的登录历史或管理员可以清除任何人的
    if (id !== req.user?.id && !req.user?.isAdmin) {
      return unauthorized(res, '权限不足');
    }
    
    const User = require('../models/User');
    const user = await User.findById(id);
    
    if (!user) {
      return notFound(res, '用户未找到');
    }
    
    user.metadata.loginHistory = [];
    await user.save();
    
    return success(res, { message: '登录历史已清除' }, '登录历史已清除');
  });
}

module.exports = new UserController();
