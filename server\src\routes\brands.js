/**
 * 品牌路由
 * 定义品牌相关的API路由
 */

const express = require('express');
const brandController = require('../controllers/brandController');
const { validateRequest } = require('../middleware/validation');
const { requireAuth, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/brands
 * @desc 获取品牌列表
 * @access Public
 * @query {number} page - 页码
 * @query {number} limit - 每页数量
 * @query {string} status - 品牌状态
 * @query {boolean} active - 是否活跃
 * @query {string} search - 搜索关键词
 * @query {string} sortBy - 排序字段
 * @query {string} sortOrder - 排序方向
 * @query {string} tags - 标签过滤
 */
router.get('/', brandController.getBrands);

/**
 * @route GET /api/brands/active
 * @desc 获取活跃品牌列表
 * @access Public
 * @query {number} limit - 限制数量
 * @query {string} sortBy - 排序字段
 * @query {string} sortOrder - 排序方向
 */
router.get('/active', brandController.getActiveBrands);

/**
 * @route GET /api/brands/popular
 * @desc 获取热门品牌
 * @access Public
 * @query {number} limit - 限制数量
 */
router.get('/popular', brandController.getPopularBrands);

/**
 * @route GET /api/brands/latest
 * @desc 获取最新品牌
 * @access Public
 * @query {number} limit - 限制数量
 */
router.get('/latest', brandController.getLatestBrands);

/**
 * @route GET /api/brands/search
 * @desc 搜索品牌
 * @access Public
 * @query {string} keyword - 搜索关键词
 * @query {string} status - 品牌状态
 * @query {number} limit - 限制数量
 */
router.get('/search', brandController.searchBrands);

/**
 * @route GET /api/brands/stats
 * @desc 获取品牌统计信息
 * @access Private (Admin)
 */
router.get('/stats', requireAuth, requireAdmin, brandController.getBrandStats);

/**
 * @route GET /api/brands/tag/:tag
 * @desc 按标签获取品牌
 * @access Public
 * @param {string} tag - 标签名称
 * @query {number} limit - 限制数量
 * @query {number} skip - 跳过数量
 */
router.get('/tag/:tag', brandController.getBrandsByTag);

/**
 * @route GET /api/brands/:id
 * @desc 根据ID获取品牌
 * @access Public
 * @param {string} id - 品牌ID
 */
router.get('/:id', brandController.getBrandById);

/**
 * @route POST /api/brands
 * @desc 创建品牌
 * @access Private (Admin)
 * @body {string} name - 品牌名称
 * @body {string} icon - 品牌图标URL
 * @body {string} description - 品牌描述
 * @body {string} website - 品牌官网
 * @body {array} tags - 品牌标签
 * @body {number} sortOrder - 排序顺序
 */
router.post('/', 
  requireAuth, 
  requireAdmin,
  validateRequest([
    'name',
    'icon'
  ]),
  brandController.createBrand
);

/**
 * @route PUT /api/brands/:id
 * @desc 更新品牌
 * @access Private (Admin)
 * @param {string} id - 品牌ID
 * @body {string} name - 品牌名称
 * @body {string} icon - 品牌图标URL
 * @body {string} description - 品牌描述
 * @body {string} website - 品牌官网
 * @body {array} tags - 品牌标签
 * @body {number} sortOrder - 排序顺序
 */
router.put('/:id', 
  requireAuth, 
  requireAdmin,
  brandController.updateBrand
);

/**
 * @route DELETE /api/brands/:id
 * @desc 删除品牌
 * @access Private (Admin)
 * @param {string} id - 品牌ID
 */
router.delete('/:id', 
  requireAuth, 
  requireAdmin,
  brandController.deleteBrand
);

/**
 * @route POST /api/brands/batch/status
 * @desc 批量更新品牌状态
 * @access Private (Admin)
 * @body {array} ids - 品牌ID数组
 * @body {string} status - 新状态
 */
router.post('/batch/status', 
  requireAuth, 
  requireAdmin,
  validateRequest(['ids', 'status']),
  brandController.batchUpdateStatus
);

/**
 * @route POST /api/brands/batch/sort
 * @desc 批量更新品牌排序
 * @access Private (Admin)
 * @body {array} updates - 更新数组 [{ id, sortOrder }]
 */
router.post('/batch/sort', 
  requireAuth, 
  requireAdmin,
  validateRequest(['updates']),
  brandController.batchUpdateSort
);

/**
 * @route POST /api/brands/:id/views
 * @desc 增加品牌浏览次数
 * @access Public
 * @param {string} id - 品牌ID
 */
router.post('/:id/views', brandController.incrementViews);

/**
 * @route POST /api/brands/:id/clicks
 * @desc 增加品牌点击次数
 * @access Public
 * @param {string} id - 品牌ID
 */
router.post('/:id/clicks', brandController.incrementClicks);

/**
 * @route POST /api/brands/:id/activate
 * @desc 激活品牌
 * @access Private (Admin)
 * @param {string} id - 品牌ID
 */
router.post('/:id/activate', 
  requireAuth, 
  requireAdmin,
  brandController.activateBrand
);

/**
 * @route POST /api/brands/:id/deactivate
 * @desc 停用品牌
 * @access Private (Admin)
 * @param {string} id - 品牌ID
 */
router.post('/:id/deactivate', 
  requireAuth, 
  requireAdmin,
  brandController.deactivateBrand
);

/**
 * @route PUT /api/brands/:id/sort
 * @desc 更新品牌排序
 * @access Private (Admin)
 * @param {string} id - 品牌ID
 * @body {number} sortOrder - 排序顺序
 */
router.put('/:id/sort', 
  requireAuth, 
  requireAdmin,
  validateRequest(['sortOrder']),
  brandController.updateBrandSort
);

/**
 * @route POST /api/brands/:id/tags
 * @desc 添加品牌标签
 * @access Private (Admin)
 * @param {string} id - 品牌ID
 * @body {string} tag - 标签名称
 */
router.post('/:id/tags', 
  requireAuth, 
  requireAdmin,
  validateRequest(['tag']),
  brandController.addBrandTag
);

/**
 * @route DELETE /api/brands/:id/tags/:tag
 * @desc 移除品牌标签
 * @access Private (Admin)
 * @param {string} id - 品牌ID
 * @param {string} tag - 标签名称
 */
router.delete('/:id/tags/:tag', 
  requireAuth, 
  requireAdmin,
  brandController.removeBrandTag
);

module.exports = router;
