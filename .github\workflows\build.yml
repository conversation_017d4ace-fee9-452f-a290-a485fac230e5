name: Build and Release

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

permissions:
  contents: write
  packages: read

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install client dependencies
      run: |
        cd client
        npm install --legacy-peer-deps

    - name: Build Electron application
      run: |
        cd client
        npm run build
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Upload artifacts (Windows)
      if: matrix.os == 'windows-latest'
      uses: actions/upload-artifact@v4
      with:
        name: windows-build
        path: |
          client/dist/*.exe
          client/dist/*.msi
          client/dist/latest.yml

    - name: Upload artifacts (macOS)
      if: matrix.os == 'macos-latest'
      uses: actions/upload-artifact@v4
      with:
        name: macos-build
        path: |
          client/dist/*.dmg
          client/dist/*.zip
          client/dist/latest-mac.yml

    - name: Upload artifacts (Linux)
      if: matrix.os == 'ubuntu-latest'
      uses: actions/upload-artifact@v4
      with:
        name: linux-build
        path: |
          client/dist/*.AppImage
          client/dist/*.deb
          client/dist/latest-linux.yml

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v') || (github.ref == 'refs/heads/main' && github.event_name == 'push')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all artifacts
      uses: actions/download-artifact@v4

    - name: Get package version
      id: package-version
      run: echo "version=$(node -p "require('./client/package.json').version")" >> $GITHUB_OUTPUT

    - name: Create Release (for tags)
      if: startsWith(github.ref, 'refs/tags/v')
      uses: softprops/action-gh-release@v1
      with:
        files: |
          windows-build/*
          macos-build/*
          linux-build/*
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Update existing release (for main branch)
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      uses: softprops/action-gh-release@v1
      with:
        tag_name: v${{ steps.package-version.outputs.version }}
        files: |
          windows-build/*
          macos-build/*
          linux-build/*
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
