/**
 * 验证中间件
 * 处理请求数据验证
 */

const { validationError } = require('../utils/response');
const { 
  validateBrandData, 
  validateUserData, 
  validateObjectId,
  validatePagination,
  validateSort
} = require('../utils/validation');

/**
 * 通用请求验证中间件
 * @param {Array} requiredFields - 必需字段数组
 * @param {Object} options - 验证选项
 */
const validateRequest = (requiredFields = [], options = {}) => {
  return (req, res, next) => {
    const { allowEmpty = false, source = 'body' } = options;
    const data = req[source];
    const errors = {};

    // 检查必需字段
    for (const field of requiredFields) {
      if (data[field] === undefined || data[field] === null) {
        errors[field] = [`${field} 是必需的`];
      } else if (!allowEmpty && data[field] === '') {
        errors[field] = [`${field} 不能为空`];
      }
    }

    if (Object.keys(errors).length > 0) {
      return validationError(res, errors, '请求数据验证失败');
    }

    next();
  };
};

/**
 * 品牌数据验证中间件
 */
const validateBrand = (req, res, next) => {
  const validation = validateBrandData(req.body);
  
  if (!validation.isValid) {
    return validationError(res, validation.errors, '品牌数据验证失败');
  }

  // 将验证后的数据替换原始数据
  req.body = validation.data;
  next();
};

/**
 * 用户数据验证中间件
 */
const validateUser = (req, res, next) => {
  const validation = validateUserData(req.body);
  
  if (!validation.isValid) {
    return validationError(res, validation.errors, '用户数据验证失败');
  }

  // 将验证后的数据替换原始数据
  req.body = validation.data;
  next();
};

/**
 * ObjectId 验证中间件
 * @param {string} paramName - 参数名称（默认为 'id'）
 */
const validateObjectIdParam = (paramName = 'id') => {
  return (req, res, next) => {
    const id = req.params[paramName];
    const validation = validateObjectId(id);
    
    if (!validation.isValid) {
      return validationError(res, { [paramName]: validation.errors }, 'ID格式验证失败');
    }

    next();
  };
};

/**
 * 分页参数验证中间件
 */
const validatePaginationParams = (req, res, next) => {
  const { page, limit } = req.query;
  const validation = validatePagination(page, limit);
  
  if (!validation.isValid) {
    return validationError(res, { pagination: validation.errors }, '分页参数验证失败');
  }

  // 将验证后的分页参数添加到请求对象
  req.pagination = validation.value;
  next();
};

/**
 * 排序参数验证中间件
 * @param {Array} allowedFields - 允许的排序字段
 */
const validateSortParams = (allowedFields = []) => {
  return (req, res, next) => {
    const { sortBy, sortOrder } = req.query;
    
    if (sortBy || sortOrder) {
      const validation = validateSort(sortBy, sortOrder, allowedFields);
      
      if (!validation.isValid) {
        return validationError(res, { sort: validation.errors }, '排序参数验证失败');
      }

      // 将验证后的排序参数添加到请求对象
      req.sort = validation.value;
    }

    next();
  };
};

/**
 * 搜索参数验证中间件
 */
const validateSearchParams = (req, res, next) => {
  const { search, keyword } = req.query;
  const searchTerm = search || keyword;

  if (searchTerm) {
    // 验证搜索关键词长度
    if (searchTerm.length < 1) {
      return validationError(res, { search: ['搜索关键词不能为空'] }, '搜索参数验证失败');
    }

    if (searchTerm.length > 100) {
      return validationError(res, { search: ['搜索关键词不能超过100个字符'] }, '搜索参数验证失败');
    }

    // 清理搜索关键词
    req.query.search = searchTerm.trim();
  }

  next();
};

/**
 * 批量操作验证中间件
 * @param {string} idsField - ID数组字段名（默认为 'ids'）
 * @param {number} maxCount - 最大操作数量（默认为 100）
 */
const validateBatchOperation = (idsField = 'ids', maxCount = 100) => {
  return (req, res, next) => {
    const ids = req.body[idsField];
    const errors = {};

    // 检查是否提供了ID数组
    if (!ids || !Array.isArray(ids)) {
      errors[idsField] = ['必须提供ID数组'];
    } else {
      // 检查数组长度
      if (ids.length === 0) {
        errors[idsField] = ['ID数组不能为空'];
      } else if (ids.length > maxCount) {
        errors[idsField] = [`一次最多只能操作 ${maxCount} 个项目`];
      } else {
        // 验证每个ID的格式
        const invalidIds = [];
        for (let i = 0; i < ids.length; i++) {
          const validation = validateObjectId(ids[i]);
          if (!validation.isValid) {
            invalidIds.push(`位置 ${i}: ${ids[i]}`);
          }
        }

        if (invalidIds.length > 0) {
          errors[idsField] = [`无效的ID格式: ${invalidIds.join(', ')}`];
        }
      }
    }

    if (Object.keys(errors).length > 0) {
      return validationError(res, errors, '批量操作验证失败');
    }

    next();
  };
};

/**
 * 文件上传验证中间件
 * @param {Object} options - 验证选项
 */
const validateFileUpload = (options = {}) => {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif'],
    required = false
  } = options;

  return (req, res, next) => {
    const file = req.file;
    const errors = {};

    if (required && !file) {
      errors.file = ['文件是必需的'];
    }

    if (file) {
      // 检查文件大小
      if (file.size > maxSize) {
        errors.file = [`文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`];
      }

      // 检查文件类型
      if (!allowedTypes.includes(file.mimetype)) {
        errors.file = [`不支持的文件类型，允许的类型: ${allowedTypes.join(', ')}`];
      }
    }

    if (Object.keys(errors).length > 0) {
      return validationError(res, errors, '文件验证失败');
    }

    next();
  };
};

/**
 * 自定义验证中间件工厂
 * @param {Function} validator - 验证函数
 * @param {string} errorMessage - 错误消息
 */
const createCustomValidator = (validator, errorMessage = '数据验证失败') => {
  return async (req, res, next) => {
    try {
      const result = await validator(req);
      
      if (result === true) {
        return next();
      }

      if (typeof result === 'object' && result.isValid === false) {
        return validationError(res, result.errors, errorMessage);
      }

      if (typeof result === 'string') {
        return validationError(res, { validation: [result] }, errorMessage);
      }

      return validationError(res, { validation: ['验证失败'] }, errorMessage);
    } catch (error) {
      console.error('自定义验证器错误:', error);
      return validationError(res, { validation: ['验证过程中发生错误'] }, errorMessage);
    }
  };
};

/**
 * 条件验证中间件
 * @param {Function} condition - 条件函数
 * @param {Function} validator - 验证中间件
 */
const conditionalValidation = (condition, validator) => {
  return (req, res, next) => {
    if (condition(req)) {
      return validator(req, res, next);
    }
    next();
  };
};

/**
 * 数据清理中间件
 * 清理和标准化输入数据
 */
const sanitizeInput = (req, res, next) => {
  // 递归清理对象
  const sanitize = (obj) => {
    if (typeof obj === 'string') {
      return obj.trim();
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitize);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitize(value);
      }
      return sanitized;
    }
    
    return obj;
  };

  // 清理请求体
  if (req.body) {
    req.body = sanitize(req.body);
  }

  // 清理查询参数
  if (req.query) {
    req.query = sanitize(req.query);
  }

  next();
};

module.exports = {
  validateRequest,
  validateBrand,
  validateUser,
  validateObjectIdParam,
  validatePaginationParams,
  validateSortParams,
  validateSearchParams,
  validateBatchOperation,
  validateFileUpload,
  createCustomValidator,
  conditionalValidation,
  sanitizeInput
};
