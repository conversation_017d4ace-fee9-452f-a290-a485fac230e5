/**
 * 品牌服务层
 * 处理品牌相关的业务逻辑
 */

const Brand = require('../models/Brand');
const { validateBrandData, validateObjectId, validatePagination, validateSort } = require('../utils/validation');
const { createError } = require('../utils/response');
const { ERROR_CODES, BRAND_STATUS, DEFAULT_SORT } = require('../constants');

/**
 * 品牌服务类
 */
class BrandService {
  /**
   * 获取品牌列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 品牌列表和分页信息
   */
  async getBrands(options = {}) {
    const {
      page = 1,
      limit = 10,
      status,
      active,
      search,
      sortBy = 'sortOrder',
      sortOrder = 'asc',
      tags
    } = options;

    // 验证分页参数
    const paginationValidation = validatePagination(page, limit);
    if (!paginationValidation.isValid) {
      throw createError(
        paginationValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 验证排序参数
    const allowedSortFields = ['name', 'createdAt', 'updatedAt', 'sortOrder', 'metadata.views'];
    const sortValidation = validateSort(sortBy, sortOrder, allowedSortFields);
    if (!sortValidation.isValid) {
      throw createError(
        sortValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 构建查询条件
    const query = {};

    // 状态过滤
    if (status) {
      query.status = status;
    } else if (active !== undefined) {
      query.status = active ? BRAND_STATUS.ACTIVE : { $ne: BRAND_STATUS.ACTIVE };
    }

    // 搜索过滤
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { name: searchRegex },
        { description: searchRegex },
        { tags: searchRegex }
      ];
    }

    // 标签过滤
    if (tags) {
      const tagArray = Array.isArray(tags) ? tags : [tags];
      query.tags = { $in: tagArray };
    }

    // 计算分页
    const { page: validPage, limit: validLimit } = paginationValidation.value;
    const skip = (validPage - 1) * validLimit;

    // 构建排序
    const sort = {};
    if (sortValidation.value.sortBy) {
      sort[sortValidation.value.sortBy] = sortValidation.value.sortOrder || 1;
    } else {
      Object.assign(sort, DEFAULT_SORT.BRANDS);
    }

    // 执行查询
    const [brands, total] = await Promise.all([
      Brand.find(query)
        .sort(sort)
        .skip(skip)
        .limit(validLimit)
        .lean(),
      Brand.countDocuments(query)
    ]);

    return {
      brands,
      pagination: {
        page: validPage,
        limit: validLimit,
        total,
        pages: Math.ceil(total / validLimit)
      }
    };
  }

  /**
   * 根据ID获取品牌
   * @param {string} id - 品牌ID
   * @returns {Promise<Object>} 品牌信息
   */
  async getBrandById(id) {
    // 验证ID格式
    const idValidation = validateObjectId(id);
    if (!idValidation.isValid) {
      throw createError(
        idValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    const brand = await Brand.findById(id).lean();
    if (!brand) {
      throw createError(
        '品牌未找到',
        ERROR_CODES.BRAND_NOT_FOUND,
        404
      );
    }

    return brand;
  }

  /**
   * 创建品牌
   * @param {Object} brandData - 品牌数据
   * @param {string} userId - 创建者ID
   * @returns {Promise<Object>} 创建的品牌
   */
  async createBrand(brandData, userId) {
    // 验证品牌数据
    const validation = validateBrandData(brandData);
    if (!validation.isValid) {
      throw createError(
        '数据验证失败',
        ERROR_CODES.VALIDATION_ERROR,
        400,
        validation.errors
      );
    }

    // 检查品牌名称是否已存在
    const existingBrand = await Brand.findOne({ 
      name: validation.data.name,
      status: { $ne: BRAND_STATUS.DELETED }
    });

    if (existingBrand) {
      throw createError(
        '品牌名称已存在',
        ERROR_CODES.BRAND_ALREADY_EXISTS,
        409
      );
    }

    // 创建品牌
    const brand = new Brand({
      ...validation.data,
      createdBy: userId
    });

    await brand.save();
    return brand.toObject();
  }

  /**
   * 更新品牌
   * @param {string} id - 品牌ID
   * @param {Object} updateData - 更新数据
   * @param {string} userId - 更新者ID
   * @returns {Promise<Object>} 更新后的品牌
   */
  async updateBrand(id, updateData, userId) {
    // 验证ID格式
    const idValidation = validateObjectId(id);
    if (!idValidation.isValid) {
      throw createError(
        idValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 验证更新数据
    const validation = validateBrandData(updateData);
    if (!validation.isValid) {
      throw createError(
        '数据验证失败',
        ERROR_CODES.VALIDATION_ERROR,
        400,
        validation.errors
      );
    }

    // 检查品牌是否存在
    const brand = await Brand.findById(id);
    if (!brand) {
      throw createError(
        '品牌未找到',
        ERROR_CODES.BRAND_NOT_FOUND,
        404
      );
    }

    // 如果更新名称，检查是否与其他品牌冲突
    if (validation.data.name && validation.data.name !== brand.name) {
      const existingBrand = await Brand.findOne({
        name: validation.data.name,
        _id: { $ne: id },
        status: { $ne: BRAND_STATUS.DELETED }
      });

      if (existingBrand) {
        throw createError(
          '品牌名称已存在',
          ERROR_CODES.BRAND_ALREADY_EXISTS,
          409
        );
      }
    }

    // 更新品牌
    Object.assign(brand, validation.data);
    brand.updatedBy = userId;
    
    await brand.save();
    return brand.toObject();
  }

  /**
   * 删除品牌（软删除）
   * @param {string} id - 品牌ID
   * @param {string} userId - 删除者ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteBrand(id, userId) {
    // 验证ID格式
    const idValidation = validateObjectId(id);
    if (!idValidation.isValid) {
      throw createError(
        idValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 检查品牌是否存在
    const brand = await Brand.findById(id);
    if (!brand) {
      throw createError(
        '品牌未找到',
        ERROR_CODES.BRAND_NOT_FOUND,
        404
      );
    }

    // 软删除
    await brand.softDelete();
    brand.updatedBy = userId;
    await brand.save();

    return { message: '品牌已删除' };
  }

  /**
   * 批量更新品牌状态
   * @param {Array} ids - 品牌ID数组
   * @param {string} status - 新状态
   * @param {string} userId - 操作者ID
   * @returns {Promise<Object>} 更新结果
   */
  async batchUpdateStatus(ids, status, userId) {
    // 验证状态
    if (!Object.values(BRAND_STATUS).includes(status)) {
      throw createError(
        '无效的状态值',
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    // 验证ID数组
    for (const id of ids) {
      const idValidation = validateObjectId(id);
      if (!idValidation.isValid) {
        throw createError(
          `无效的品牌ID: ${id}`,
          ERROR_CODES.VALIDATION_ERROR,
          400
        );
      }
    }

    // 批量更新
    const result = await Brand.updateMany(
      { _id: { $in: ids } },
      { 
        status,
        updatedBy: userId,
        updatedAt: new Date()
      }
    );

    return {
      message: `已更新 ${result.modifiedCount} 个品牌的状态`,
      modifiedCount: result.modifiedCount
    };
  }

  /**
   * 获取活跃品牌
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 活跃品牌列表
   */
  async getActiveBrands(options = {}) {
    const { limit, sortBy = 'sortOrder', sortOrder = 'asc' } = options;

    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    if (sortBy !== 'createdAt') {
      sort.createdAt = -1;
    }

    let query = Brand.find({ status: BRAND_STATUS.ACTIVE }).sort(sort);
    
    if (limit) {
      query = query.limit(parseInt(limit, 10));
    }

    return await query.lean();
  }

  /**
   * 搜索品牌
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  async searchBrands(keyword, options = {}) {
    const { status = BRAND_STATUS.ACTIVE, limit = 20 } = options;

    if (!keyword || keyword.trim().length === 0) {
      return [];
    }

    const brands = await Brand.search(keyword.trim(), {
      status,
      limit: parseInt(limit, 10)
    }).lean();

    return brands;
  }

  /**
   * 获取品牌统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getBrandStats() {
    const stats = await Brand.getStats();
    
    // 格式化统计结果
    const formattedStats = {
      total: 0,
      active: 0,
      inactive: 0,
      pending: 0,
      deleted: 0,
      totalViews: 0,
      totalClicks: 0
    };

    stats.forEach(stat => {
      const status = stat._id;
      formattedStats.total += stat.count;
      formattedStats[status] = stat.count;
      formattedStats.totalViews += stat.totalViews || 0;
      formattedStats.totalClicks += stat.totalClicks || 0;
    });

    return formattedStats;
  }

  /**
   * 增加品牌浏览次数
   * @param {string} id - 品牌ID
   * @returns {Promise<Object>} 更新结果
   */
  async incrementViews(id) {
    const idValidation = validateObjectId(id);
    if (!idValidation.isValid) {
      throw createError(
        idValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    const brand = await Brand.findById(id);
    if (!brand) {
      throw createError(
        '品牌未找到',
        ERROR_CODES.BRAND_NOT_FOUND,
        404
      );
    }

    await brand.incrementViews();
    return { message: '浏览次数已更新' };
  }

  /**
   * 增加品牌点击次数
   * @param {string} id - 品牌ID
   * @returns {Promise<Object>} 更新结果
   */
  async incrementClicks(id) {
    const idValidation = validateObjectId(id);
    if (!idValidation.isValid) {
      throw createError(
        idValidation.errors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    const brand = await Brand.findById(id);
    if (!brand) {
      throw createError(
        '品牌未找到',
        ERROR_CODES.BRAND_NOT_FOUND,
        404
      );
    }

    await brand.incrementClicks();
    return { message: '点击次数已更新' };
  }
}

module.exports = new BrandService();
