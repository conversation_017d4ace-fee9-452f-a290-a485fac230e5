/**
 * 消息常量定义
 * 统一管理系统中的各种消息文本
 */

/**
 * 成功消息
 */
const SUCCESS_MESSAGES = {
  // 通用
  CREATED: '创建成功',
  UPDATED: '更新成功',
  DELETED: '删除成功',
  RETRIEVED: '获取成功',
  UPLOADED: '上传成功',
  
  // 品牌相关
  BRAND_CREATED: '品牌创建成功',
  BRAND_UPDATED: '品牌更新成功',
  BRAND_DELETED: '品牌删除成功',
  BRAND_ACTIVATED: '品牌激活成功',
  BRAND_DEACTIVATED: '品牌停用成功',
  
  // 用户相关
  USER_CREATED: '用户创建成功',
  USER_UPDATED: '用户更新成功',
  USER_DELETED: '用户删除成功',
  USER_ACTIVATED: '用户激活成功',
  USER_DEACTIVATED: '用户停用成功',
  AUTH_CODE_REGENERATED: '授权码重新生成成功',
  
  // 文件相关
  FILE_UPLOADED: '文件上传成功',
  FILE_DELETED: '文件删除成功',
  FILE_UPDATED: '文件信息更新成功',
  FILES_CLEANED: '文件清理完成'
};

/**
 * 错误消息
 */
const ERROR_MESSAGES = {
  // 通用
  INTERNAL_ERROR: '服务器内部错误',
  NOT_FOUND: '资源不存在',
  UNAUTHORIZED: '未授权访问',
  FORBIDDEN: '禁止访问',
  VALIDATION_ERROR: '数据验证失败',
  DUPLICATE_ERROR: '数据已存在',
  
  // 品牌相关
  BRAND_NOT_FOUND: '品牌不存在',
  BRAND_NAME_EXISTS: '品牌名称已存在',
  BRAND_ICON_INVALID: '品牌图标无效',
  
  // 用户相关
  USER_NOT_FOUND: '用户不存在',
  USERNAME_EXISTS: '用户名已存在',
  EMAIL_EXISTS: '邮箱已存在',
  AUTH_CODE_EXISTS: '授权码已存在',
  INVALID_AUTH_CODE: '无效的授权码',
  
  // 文件相关
  FILE_NOT_FOUND: '文件不存在',
  FILE_TOO_LARGE: '文件大小超过限制',
  FILE_TYPE_NOT_ALLOWED: '不支持的文件类型',
  FILE_UPLOAD_FAILED: '文件上传失败',
  FILE_DELETE_FAILED: '文件删除失败',
  NO_FILE_PROVIDED: '未提供文件',
  
  // 权限相关
  INSUFFICIENT_PERMISSIONS: '权限不足',
  ADMIN_REQUIRED: '需要管理员权限',
  OWNER_REQUIRED: '只有所有者可以执行此操作'
};

/**
 * 警告消息
 */
const WARNING_MESSAGES = {
  DEPRECATED_API: 'API已弃用，请使用新版本',
  RATE_LIMIT_WARNING: '请求频率过高，请稍后再试',
  STORAGE_LIMIT_WARNING: '存储空间即将用完',
  FILE_SIZE_WARNING: '文件大小较大，上传可能需要较长时间'
};

/**
 * 信息消息
 */
const INFO_MESSAGES = {
  PROCESSING: '正在处理中...',
  UPLOADING: '正在上传...',
  DOWNLOADING: '正在下载...',
  CLEANING: '正在清理...',
  MAINTENANCE: '系统维护中，请稍后再试'
};

module.exports = {
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
  WARNING_MESSAGES,
  INFO_MESSAGES
};
