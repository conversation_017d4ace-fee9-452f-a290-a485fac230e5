// 首先加载环境变量
require('dotenv').config();

const { app, BrowserWindow, Menu, dialog, ipcMain, shell, net } = require('electron');
const { autoUpdater } = require('electron-updater');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const i18n = require('./i18n');
const { APP_CONFIG, getWindowConfig, printEnvConfig, isDevelopment } = require('../shared/config/env');
const isDev = isDevelopment() || process.argv.includes('--dev');

// 保持对窗口对象的全局引用，如果不这么做的话，当JavaScript对象被
// 垃圾回收的时候，窗口会被自动地关闭
let mainWindow;
let currentUser = null; // 当前登录用户
let appSettings = {}; // 应用设置

// 配置自动更新
function configureAutoUpdater() {
  // 设置更新服务器
  if (!isDev) {
    autoUpdater.checkForUpdatesAndNotify();
  }

  // 监听更新事件
  autoUpdater.on('checking-for-update', () => {
    console.log('正在检查更新...');
  });

  autoUpdater.on('update-available', (info) => {
    console.log('发现新版本:', info.version);
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '发现新版本',
      message: `发现新版本 ${info.version}，正在下载...`,
      buttons: ['确定']
    });
  });

  autoUpdater.on('update-not-available', (info) => {
    console.log('当前已是最新版本');
  });

  autoUpdater.on('error', (err) => {
    console.error('更新出错:', err);
  });

  autoUpdater.on('download-progress', (progressObj) => {
    let log_message = "下载速度: " + progressObj.bytesPerSecond;
    log_message = log_message + ' - 已下载 ' + progressObj.percent + '%';
    log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
    console.log(log_message);
  });

  autoUpdater.on('update-downloaded', (info) => {
    console.log('更新下载完成');
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '更新下载完成',
      message: '新版本已下载完成，是否立即重启应用以完成更新？',
      buttons: ['立即重启', '稍后重启']
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  });
}

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 500,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    show: false, // 先不显示，等加载完成后再显示
    resizable: false, // 登录窗口不可调整大小
    center: true
  });

  // 首先加载登录界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/login.html'));

  // 窗口加载完成后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 当 window 被关闭，这个事件会被触发
  mainWindow.on('closed', () => {
    // 取消引用 window 对象，如果你的应用支持多窗口的话，
    // 通常会把多个 window 对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
    currentUser = null;
  });

  // 开发环境下打开开发者工具
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }
}

// 切换到主界面
function switchToMainApp() {
  if (!mainWindow) return;

  // 调整窗口大小和属性
  mainWindow.setSize(1200, 800);
  mainWindow.setResizable(true);
  mainWindow.center();

  // 加载主应用界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/index.html'));
}

// 切换到管理界面
function switchToAdminPanel() {
  if (!mainWindow) return;

  // 调整窗口大小和属性
  mainWindow.setSize(1400, 900);
  mainWindow.setResizable(true);
  mainWindow.center();

  // 加载管理界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/admin.html'));
}

// 切换到设置界面
function switchToSettings() {
  if (!mainWindow) return;

  // 调整窗口大小和属性
  mainWindow.setSize(1000, 800);
  mainWindow.setResizable(true);
  mainWindow.center();

  // 加载设置界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/settings.html'));
}

// 切换到登录界面
function switchToLogin() {
  if (!mainWindow) return;

  // 调整窗口大小和属性
  mainWindow.setSize(500, 600);
  mainWindow.setResizable(false);
  mainWindow.center();

  // 加载登录界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/login.html'));
}

// Electron 会在初始化后并准备
// 创建浏览器窗口时，调用这个函数。
// 部分 API 在 ready 事件触发后才能使用。
app.whenReady().then(async () => {
  // 打印环境配置信息（仅开发环境）
  printEnvConfig();

  // 先加载应用设置
  await loadAppSettings();

  // 然后初始化国际化
  await initializeI18n();

  createWindow();

  // 配置自动更新
  configureAutoUpdater();

  app.on('activate', () => {
    // 在macOS上，当单击dock图标并且没有其他窗口打开时，
    // 通常在应用程序中重新创建一个窗口。
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// 当全部窗口关闭时退出。
app.on('window-all-closed', () => {
  // 在 macOS 上，除非用户用 Cmd + Q 确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') app.quit();
});

// 手动检查更新
ipcMain.handle('check-for-updates', async () => {
  if (!isDev) {
    try {
      const result = await autoUpdater.checkForUpdates();
      return result;
    } catch (error) {
      console.error('检查更新失败:', error);
      return null;
    }
  }
  return null;
});

// 获取应用版本
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

// 环境变量相关 IPC 处理
ipcMain.handle('get-env-var', (event, key) => {
  return APP_CONFIG[key] || process.env[key] || null;
});

ipcMain.handle('get-api-config', () => {
  const { getConfig } = require('../shared/config/api');
  return getConfig();
});

ipcMain.handle('get-app-config', () => {
  return {
    name: APP_CONFIG.APP_NAME,
    version: APP_CONFIG.APP_VERSION,
    environment: APP_CONFIG.NODE_ENV,
    theme: APP_CONFIG.DEFAULT_THEME,
    language: APP_CONFIG.DEFAULT_LANGUAGE,
    debugMode: APP_CONFIG.DEBUG_MODE,
    devToolsEnabled: APP_CONFIG.DEV_TOOLS_ENABLED
  };
});

ipcMain.handle('is-development', () => {
  return isDevelopment();
});

ipcMain.handle('get-window-config', (event, windowType) => {
  return getWindowConfig(windowType);
});

// 获取当前用户信息
ipcMain.handle('get-current-user', () => {
  return currentUser;
});

// 验证授权码
ipcMain.handle('verify-auth-code', async (event, authCode) => {
  try {
    console.log('开始验证授权码:', authCode);
    return new Promise((resolve) => {
      const { getApiBaseUrl } = require('../shared/config/env');
      const apiBaseUrl = getApiBaseUrl();
      const request = net.request({
        method: 'POST',
        url: `${apiBaseUrl}/api/users/auth`
      });

      request.setHeader('Content-Type', 'application/json');

      let responseData = '';

      request.on('response', (response) => {
        response.on('data', (chunk) => {
          responseData += chunk;
        });

        response.on('end', () => {
          try {
            const result = JSON.parse(responseData);
            console.log('服务器响应:', result);

            if (result.status === 'success') {
              // 将认证码添加到用户对象中，确保一致的字段名
              currentUser = {
                ...result.data.user,
                authCode: authCode  // 统一使用 authCode 字段
              };
              resolve({
                success: true,
                user: currentUser,
                message: result.message || '授权验证成功'
              });
            } else {
              resolve({
                success: false,
                error: result.message || result.error || '授权验证失败'
              });
            }
          } catch (parseError) {
            console.error('解析响应失败:', parseError);
            console.error('原始响应数据:', responseData);
            resolve({
              success: false,
              error: '服务器响应格式错误'
            });
          }
        });
      });

      request.on('error', (error) => {
        console.error('网络请求失败:', error);
        resolve({
          success: false,
          error: '无法连接到服务器，请确保后端服务正在运行'
        });
      });

      request.write(JSON.stringify({ authCode }));
      request.end();
    });
  } catch (error) {
    console.error('授权验证失败:', error);
    return {
      success: false,
      error: '授权验证过程中发生错误'
    };
  }
});

// 授权成功后切换到主界面
ipcMain.handle('auth-success', async (event, user) => {
  try {
    currentUser = user;
    switchToMainApp();
    return { success: true };
  } catch (error) {
    console.error('切换到主界面失败:', error);
    return { success: false, error: '切换到主界面失败' };
  }
});

// 打开管理面板
ipcMain.handle('open-admin-panel', async () => {
  try {
    // 检查用户权限
    if (!currentUser) {
      return {
        success: false,
        error: '请先登录'
      };
    }

    if (currentUser.userType !== 'admin') {
      return {
        success: false,
        error: '只有管理员才能访问管理面板'
      };
    }

    // 切换到内置管理界面
    switchToAdminPanel();

    return {
      success: true,
      message: '管理面板已打开'
    };
  } catch (error) {
    console.error('打开管理面板失败:', error);
    return {
      success: false,
      error: '打开管理面板失败'
    };
  }
});

// 切换到主界面
ipcMain.handle('switch-to-main', async () => {
  try {
    switchToMainApp();
    return { success: true };
  } catch (error) {
    console.error('切换到主界面失败:', error);
    return { success: false, error: '切换到主界面失败' };
  }
});

// 退出登录
ipcMain.handle('logout', async () => {
  try {
    // 清除当前用户信息
    currentUser = null;

    // 切换回登录界面
    switchToLogin();

    return { success: true };
  } catch (error) {
    console.error('退出登录失败:', error);
    return { success: false, error: '退出登录失败' };
  }
});

// 打开设置页面
ipcMain.handle('open-settings', async () => {
  try {
    switchToSettings();
    return { success: true };
  } catch (error) {
    console.error('打开设置页面失败:', error);
    return { success: false, error: '打开设置页面失败' };
  }
});

// 初始化国际化
async function initializeI18n() {
  try {
    const language = appSettings.language || 'zh-CN';
    await i18n.init(language);
    console.log(`国际化初始化完成，当前语言: ${language}`);
  } catch (error) {
    console.error('国际化初始化失败:', error);
    await i18n.init('zh-CN'); // 回退到中文
  }
}

// 加载应用设置
async function loadAppSettings() {
  try {
    const settingsPath = path.join(app.getPath('userData'), 'settings.json');
    console.log('设置文件路径:', settingsPath);

    if (fs.existsSync(settingsPath)) {
      const settingsData = fs.readFileSync(settingsPath, 'utf8');
      appSettings = JSON.parse(settingsData);
      console.log('从文件加载的设置:', appSettings);
    } else {
      // 使用默认设置
      appSettings = {
        language: 'zh-CN',
        autoStart: false,
        minimizeToTray: false,
        autoUpdate: true,
        theme: 'auto'
      };
      await saveAppSettings();
      console.log('使用默认设置:', appSettings);
    }
    console.log('应用设置已加载:', appSettings);
  } catch (error) {
    console.error('加载应用设置失败:', error);
    appSettings = {
      language: 'zh-CN',
      autoStart: false,
      minimizeToTray: false,
      autoUpdate: true,
      theme: 'auto'
    };
  }
}

// 保存应用设置
async function saveAppSettings() {
  try {
    const settingsPath = path.join(app.getPath('userData'), 'settings.json');
    fs.writeFileSync(settingsPath, JSON.stringify(appSettings, null, 2));
    console.log('应用设置已保存');
  } catch (error) {
    console.error('保存应用设置失败:', error);
  }
}

// 获取单个设置
ipcMain.handle('get-setting', async (event, key) => {
  return appSettings[key];
});

// 获取所有设置
ipcMain.handle('get-all-settings', async () => {
  return appSettings;
});

// 保存所有设置
ipcMain.handle('save-all-settings', async (event, settings) => {
  try {
    const oldLanguage = appSettings.language;
    appSettings = { ...appSettings, ...settings };
    await saveAppSettings();

    // 如果语言发生变化，更新国际化
    if (settings.language && settings.language !== i18n.getCurrentLanguage()) {
      await i18n.switchLanguage(settings.language);

      // 通知所有窗口语言已更改
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send('language-changed', settings.language);
      });
    }

    return { success: true };
  } catch (error) {
    console.error('保存设置失败:', error);
    return { success: false, error: error.message };
  }
});

// 获取国际化文本
ipcMain.handle('get-i18n-texts', async (event, language) => {
  try {
    console.log('主进程收到获取国际化文本请求，语言:', language);
    console.log('主进程当前语言:', i18n.getCurrentLanguage());

    if (language && language !== i18n.getCurrentLanguage()) {
      console.log('切换语言从', i18n.getCurrentLanguage(), '到', language);
      await i18n.switchLanguage(language);
    }

    const texts = i18n.getAllTexts();
    console.log('主进程返回的文本键:', Object.keys(texts).slice(0, 10));
    console.log('app.title文本:', texts['app.title']);
    console.log('使用t方法获取app.title:', i18n.t('app.title'));
    console.log('texts.app:', texts.app);
    console.log('texts.app.title:', texts.app ? texts.app.title : 'app对象不存在');

    return texts;
  } catch (error) {
    console.error('获取国际化文本失败:', error);
    return {};
  }
});

// 获取可用语言列表
ipcMain.handle('get-available-languages', async () => {
  return i18n.getAvailableLanguages();
});

// 翻译文本
ipcMain.handle('translate', async (event, key, params) => {
  return i18n.t(key, params);
});

// 应用主题
ipcMain.handle('apply-theme', async (event, theme) => {
  try {
    // 更新设置
    appSettings.theme = theme;
    await saveAppSettings();

    // 通知所有窗口主题已更改
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('theme-changed', theme);
    });

    return { success: true };
  } catch (error) {
    console.error('应用主题失败:', error);
    return { success: false, error: error.message };
  }
});

// 支持更新通道的检查更新
ipcMain.handle('check-for-updates-with-channel', async (event, options) => {
  try {
    const { channel, customUrl } = options;

    // 根据通道选择更新源
    let updateUrl;
    switch (channel) {
      case 'github':
        updateUrl = 'https://github.com/laixiao/AiTools/releases';
        break;
      case 'gitee':
        updateUrl = 'https://gitee.com/laixiao/AiTools/releases';
        break;
      case 'custom':
        updateUrl = customUrl;
        break;
      default:
        updateUrl = 'https://github.com/laixiao/AiTools/releases';
    }

    if (!updateUrl) {
      return { success: false, error: '更新地址未配置' };
    }

    console.log(`检查更新，通道: ${channel}, 地址: ${updateUrl}`);

    // 在开发模式下模拟检查更新
    if (isDev) {
      return {
        success: true,
        hasUpdate: false,
        message: '开发模式下的模拟检查更新',
        channel: channel,
        url: updateUrl
      };
    }

    // 在生产模式下使用 autoUpdater
    return new Promise((resolve) => {
      // 设置更新源
      autoUpdater.setFeedURL({
        provider: 'github',
        owner: 'laixiao',
        repo: 'AiTools'
      });

      // 监听更新事件
      const onUpdateAvailable = (info) => {
        autoUpdater.removeAllListeners();
        resolve({
          success: true,
          hasUpdate: true,
          version: info.version,
          channel: channel,
          url: updateUrl
        });
      };

      const onUpdateNotAvailable = () => {
        autoUpdater.removeAllListeners();
        resolve({
          success: true,
          hasUpdate: false,
          channel: channel,
          url: updateUrl
        });
      };

      const onError = (error) => {
        autoUpdater.removeAllListeners();
        resolve({
          success: false,
          error: error.message,
          channel: channel,
          url: updateUrl
        });
      };

      autoUpdater.once('update-available', onUpdateAvailable);
      autoUpdater.once('update-not-available', onUpdateNotAvailable);
      autoUpdater.once('error', onError);

      // 开始检查更新
      autoUpdater.checkForUpdates();

      // 10秒超时
      setTimeout(() => {
        autoUpdater.removeAllListeners();
        resolve({
          success: false,
          error: '检查更新超时',
          channel: channel,
          url: updateUrl
        });
      }, 10000);
    });

  } catch (error) {
    console.error('检查更新失败:', error);
    return { success: false, error: error.message };
  }
});
