/**
 * 响应工具函数
 * 统一处理 API 响应格式
 */

const { HTTP_STATUS, RESPONSE_STATUS, ERROR_CODES, ERROR_MESSAGES } = require('../constants');

/**
 * 成功响应
 * @param {Object} res - Express 响应对象
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @param {number} statusCode - HTTP 状态码
 */
function success(res, data = null, message = '操作成功', statusCode = HTTP_STATUS.OK) {
  const response = {
    status: RESPONSE_STATUS.SUCCESS,
    message,
    data,
    timestamp: new Date().toISOString()
  };
  
  return res.status(statusCode).json(response);
}

/**
 * 创建成功响应
 * @param {Object} res - Express 响应对象
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 */
function created(res, data = null, message = '创建成功') {
  return success(res, data, message, HTTP_STATUS.CREATED);
}

/**
 * 无内容响应
 * @param {Object} res - Express 响应对象
 * @param {string} message - 响应消息
 */
function noContent(res, message = '操作成功') {
  const response = {
    status: RESPONSE_STATUS.SUCCESS,
    message,
    timestamp: new Date().toISOString()
  };
  
  return res.status(HTTP_STATUS.NO_CONTENT).json(response);
}

/**
 * 分页响应
 * @param {Object} res - Express 响应对象
 * @param {Array} data - 数据数组
 * @param {Object} pagination - 分页信息
 * @param {string} message - 响应消息
 */
function paginated(res, data = [], pagination = {}, message = '获取成功') {
  const response = {
    status: RESPONSE_STATUS.SUCCESS,
    message,
    data,
    pagination: {
      page: pagination.page || 1,
      limit: pagination.limit || 10,
      total: pagination.total || 0,
      pages: Math.ceil((pagination.total || 0) / (pagination.limit || 10))
    },
    timestamp: new Date().toISOString()
  };
  
  return res.status(HTTP_STATUS.OK).json(response);
}

/**
 * 错误响应
 * @param {Object} res - Express 响应对象
 * @param {string} message - 错误消息
 * @param {number} statusCode - HTTP 状态码
 * @param {string} errorCode - 错误代码
 * @param {*} details - 错误详情
 */
function error(res, message = '操作失败', statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR, errorCode = ERROR_CODES.INTERNAL_ERROR, details = null) {
  const response = {
    status: RESPONSE_STATUS.ERROR,
    message,
    errorCode,
    timestamp: new Date().toISOString()
  };
  
  if (details) {
    response.details = details;
  }
  
  return res.status(statusCode).json(response);
}

/**
 * 验证错误响应
 * @param {Object} res - Express 响应对象
 * @param {*} errors - 验证错误详情
 * @param {string} message - 错误消息
 */
function validationError(res, errors = null, message = '数据验证失败') {
  return error(
    res,
    message,
    HTTP_STATUS.BAD_REQUEST,
    ERROR_CODES.VALIDATION_ERROR,
    errors
  );
}

/**
 * 未找到错误响应
 * @param {Object} res - Express 响应对象
 * @param {string} message - 错误消息
 */
function notFound(res, message = '资源未找到') {
  return error(
    res,
    message,
    HTTP_STATUS.NOT_FOUND,
    ERROR_CODES.NOT_FOUND
  );
}

/**
 * 未授权错误响应
 * @param {Object} res - Express 响应对象
 * @param {string} message - 错误消息
 */
function unauthorized(res, message = '未授权访问') {
  return error(
    res,
    message,
    HTTP_STATUS.UNAUTHORIZED,
    ERROR_CODES.UNAUTHORIZED
  );
}

/**
 * 禁止访问错误响应
 * @param {Object} res - Express 响应对象
 * @param {string} message - 错误消息
 */
function forbidden(res, message = '权限不足') {
  return error(
    res,
    message,
    HTTP_STATUS.FORBIDDEN,
    ERROR_CODES.FORBIDDEN
  );
}

/**
 * 冲突错误响应
 * @param {Object} res - Express 响应对象
 * @param {string} message - 错误消息
 */
function conflict(res, message = '资源冲突') {
  return error(
    res,
    message,
    HTTP_STATUS.CONFLICT,
    ERROR_CODES.DUPLICATE_KEY
  );
}

/**
 * 服务器错误响应
 * @param {Object} res - Express 响应对象
 * @param {string} message - 错误消息
 * @param {*} details - 错误详情
 */
function serverError(res, message = '服务器内部错误', details = null) {
  return error(
    res,
    message,
    HTTP_STATUS.INTERNAL_SERVER_ERROR,
    ERROR_CODES.INTERNAL_ERROR,
    details
  );
}

/**
 * 数据库错误响应
 * @param {Object} res - Express 响应对象
 * @param {Error} err - 数据库错误对象
 */
function databaseError(res, err) {
  let message = '数据库操作失败';
  let errorCode = ERROR_CODES.DATABASE_ERROR;
  let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
  
  // 处理 MongoDB 特定错误
  if (err.code === 11000) {
    message = '数据已存在';
    errorCode = ERROR_CODES.DUPLICATE_KEY;
    statusCode = HTTP_STATUS.CONFLICT;
  } else if (err.name === 'ValidationError') {
    message = '数据验证失败';
    errorCode = ERROR_CODES.VALIDATION_ERROR;
    statusCode = HTTP_STATUS.BAD_REQUEST;
  } else if (err.name === 'CastError') {
    message = '无效的数据格式';
    errorCode = ERROR_CODES.VALIDATION_ERROR;
    statusCode = HTTP_STATUS.BAD_REQUEST;
  }
  
  return error(res, message, statusCode, errorCode, {
    name: err.name,
    code: err.code
  });
}

/**
 * 处理异步错误的包装器
 * @param {Function} fn - 异步函数
 */
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * 格式化验证错误
 * @param {Object} validationResult - 验证结果
 */
function formatValidationErrors(validationResult) {
  if (!validationResult.errors) return null;
  
  const errors = {};
  
  for (const error of validationResult.errors) {
    if (!errors[error.path]) {
      errors[error.path] = [];
    }
    errors[error.path].push({
      message: error.msg,
      value: error.value,
      location: error.location
    });
  }
  
  return errors;
}

/**
 * 创建标准化的错误对象
 * @param {string} message - 错误消息
 * @param {string} code - 错误代码
 * @param {number} statusCode - HTTP 状态码
 * @param {*} details - 错误详情
 */
function createError(message, code = ERROR_CODES.INTERNAL_ERROR, statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR, details = null) {
  const error = new Error(message);
  error.code = code;
  error.statusCode = statusCode;
  error.details = details;
  return error;
}

module.exports = {
  success,
  created,
  noContent,
  paginated,
  error,
  validationError,
  notFound,
  unauthorized,
  forbidden,
  conflict,
  serverError,
  databaseError,
  asyncHandler,
  formatValidationErrors,
  createError
};
